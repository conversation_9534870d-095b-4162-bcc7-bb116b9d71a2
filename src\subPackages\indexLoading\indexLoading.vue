<template>
  <view class="cardimg" :style="{ backgroundImage: `url(${bg})` }">
    <!-- <view class="cardimg" :style="{ background: `#fff` }"> -->
    <view class="foot-bottom">
      <view class="foot">
        <view v-for="(item, index) in data" v-show="item.show" :key="index" class="foot-tip">{{ item.label }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { onHide, onShow } from '@dcloudio/uni-app'
import { reactive, Ref, ref } from 'vue'
import { useI18n } from 'vue-i18n'
// import page1 from '@/static/image/login/page_cn.jpg'
// import page2 from '@/static/image/login/page_jp.jpg'
// import page3 from '@/static/image/login/page_en.jpg'
// import page4 from '@/static/image/login/page_fr.jpg'
// import page5 from '@/static/image/login/page_sp.jpg'
// import { useCounterStore } from '@/store/store'
import { check_token } from '@/api/index'
import bg_jp from '@/static/image/login/Loading2.png'
// import bg_jp from '@/static/image/login/loading.png'
// import bg_cn from '@/static/image/login/loading.jpg'
// import bg_sp from '@/static/image/login/loading.jpg'
// import bg_fr from '@/static/image/login/loading.jpg'
// import bg_en from '@/static/image/login/loading.jpg'

const bg = ref(bg_jp)
onShow(() => {
  // switch (locale.value) {
  //   case 'jp':
  //     bg.value = bg_jp
  //     break
  //   case 'cn':
  //     bg.value = bg_cn
  //     break
  //   case 'sp':
  //     bg.value = bg_sp
  //     break
  //   case 'fr':
  //     bg.value = bg_fr
  //     break
  //   case 'en':
  //     bg.value = bg_en
  //     break
  //   default:
  //     bg.value = bg_jp
  //     break
  // }
  bg.value = bg_jp
  // console.log(bg.value, 'bgggggggggggg')
})

const { t } = useI18n()
// const store = useCounterStore()

// const page = computed(() => {
//   switch (locale.value) {
//     case 'cn':
//       return page1
//     case 'jp':
//       return page2
//     case 'en':
//       return page3
//     case 'fr':
//       return page4
//     case 'sp':
//       return page5

//     default:
//       return page2
//   }
// })

const data = reactive([
  { label: `${t('indexLoading.label1')}`, show: false },
  { label: `${t('indexLoading.label2')}`, show: false },
  { label: `${t('indexLoading.label3')}`, show: false },
  { label: `${t('indexLoading.label4')}`, show: false },
  { label: `${t('indexLoading.label5')}`, show: false },
  { label: `${t('indexLoading.label6')}`, show: false }
])

onShow(() => {
  checkToken()
  startTimer()
})
onHide(() => {
  clearInterval(timer.value)
})
let islogin = false
const checkToken = async () => {
  const res = await check_token()
  if (res.code === 1) {
    islogin = res.data.login
  }
}

const timer: Ref<any> = ref(null)
const startTimer = () => {
  let index = 0
  timer.value = setInterval(() => {
    if (index >= data.length) {
      clearInterval(timer.value)
      // store.getUserInfo()
      // console.log(uni.getStorageSync('token'))
      if (!islogin) {
        uni.navigateTo({
          url: '/subPackages/login/login'
        })
      } else {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }

      return
    }
    refreshData(index++)
  }, 500)
}
const refreshData = (index: any) => {
  data[index].show = true
}
</script>

<style lang="scss">
.foot-bottom {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: fixed;
  right: 2.0625rem;
  bottom: 0;
}
.foot {
  // text-align: right;
  padding-bottom: 66px;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: flex-start;
}
.foot-tip {
  color: #000;
  font-size: 15px;
  margin-top: 11px;
  width: 215px;
}
.cardimg {
  // background-image: url('@/static/image/login/page.jpg');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  -webkit-transition-duration: 0.5s;
  -moz-transition-duration: 0.5s;
  -o-transition-duration: 0.5s;
  min-height: calc(var(--vh) * 100);
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}
</style>
