<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { ref } from 'vue'
import { getUserMessageApi, getUserMessageDetailApi } from '@/api/message'
import { onShow } from '@dcloudio/uni-app'
import { timestampToDate2 } from '@/common/common'
import { useCounterStore } from '@/store/store'
const store = useCounterStore()
const { t } = useI18n()
const UserMessageList = ref([])
const page = ref(1)

onShow(() => {
  UserMessageList.value = []
  page.value = 1
  getUserMessage()
  getUserMessageDetail()
})

const getUserMessage = async () => {
  const res = await getUserMessageApi({
    page: page.value
  })
  if (res.code === 1) {
    const data = res.data.data
    UserMessageList.value.push(...data)
    if (data.length !== 0) {
      page.value += 1
    }
  }
}
const getUserMessageDetail = async () => {
  await getUserMessageDetailApi()
}
</script>
<template>
  <Navigater :title="t('message.xiaoxi')" />
  <view>
    <scroll-view scroll-y :style="{ height: `calc(${store.pageHeight} - 3.125rem)` }" @scrolltolower="getUserMessage">
      <view v-for="(item, index) in UserMessageList" :key="index">
        <view class="createtime">{{ timestampToDate2(item.createtime) }}</view>
        <view class="content">{{ item.content }}</view>
      </view>
      <NotData v-if="UserMessageList.length === 0" />
    </scroll-view>
  </view>
</template>
<style lang="scss" scoped>
.content {
  color: #fff;
  background-color: rgba(10, 31, 54, 0.8);
  margin: 0.625rem;
  padding: 0.9375rem 0.625rem;
  font-size: $uni-font-size-1;
  border-radius: 0.625rem;
}
.createtime {
  font-size: $uni-font-size-1;
  color: #747474;
  text-align: center;
  margin-top: 0.625rem;
}
</style>
