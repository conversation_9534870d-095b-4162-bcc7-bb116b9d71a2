{"name": "japan11", "version": "0.0.0", "license": "MIT", "scripts": {"dev:app": "uni -p app", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0-alpha-3040220220310005", "@dcloudio/uni-app-plus": "^3.0.0-alpha-3040220220310005", "@dcloudio/uni-components": "^3.0.0-alpha-3040220220310005", "@dcloudio/uni-h5": "^3.0.0-alpha-3040220220310005", "@dcloudio/uni-mp-alipay": "^3.0.0-alpha-3040220220310005", "@dcloudio/uni-mp-baidu": "^3.0.0-alpha-3040220220310005", "@dcloudio/uni-mp-kuaishou": "^3.0.0-alpha-3040220220310005", "@dcloudio/uni-mp-lark": "^3.0.0-alpha-3040220220310005", "@dcloudio/uni-mp-qq": "^3.0.0-alpha-3040220220310005", "@dcloudio/uni-mp-toutiao": "^3.0.0-alpha-3040220220310005", "@dcloudio/uni-mp-weixin": "^3.0.0-alpha-3040220220310005", "@dcloudio/uni-quickapp-webview": "^3.0.0-alpha-3040220220310005", "@dcloudio/uni-ui": "^1.4.12", "@vitejs/plugin-vue": "2.3.3", "animate.css": "^4.1.1", "axios": "^1.7.4", "echarts": "^5.5.1", "klinecharts": "^9.8.10", "pinia": "^2.0.12", "vant": "^4.9.4", "vue": "^3.2.31", "vue-i18n": "^9.14.0"}, "devDependencies": {"@dcloudio/types": "^2.5.18", "@dcloudio/uni-automator": "^3.0.0-alpha-3040220220310005", "@dcloudio/uni-cli-shared": "^3.0.0-alpha-3040220220310005", "@dcloudio/vite-plugin-uni": "^3.0.0-alpha-3040220220310005", "@types/node": "^17.0.23", "@typescript-eslint/eslint-plugin": "^5.17.0", "@typescript-eslint/parser": "^5.17.0", "autoprefixer": "^10.4.20", "eslint": "^7.32.0", "eslint-config-prettier": "^8.5.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.25.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-promise": "^5.2.0", "eslint-plugin-vue": "^8.5.0", "postcss": "^8.4.41", "prettier": "^2.6.1", "sass": "^1.49.9", "sass-loader": "13", "tailwindcss": "^3.4.10", "typescript": "^4.6.2", "vite": "^5.4.10", "vite-plugin-eslint": "^1.3.0"}, "main": "index.js", "repository": "http://************:9070/root/japan.git", "author": "ran <<EMAIL>>"}