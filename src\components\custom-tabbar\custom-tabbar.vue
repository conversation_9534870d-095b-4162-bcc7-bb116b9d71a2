<template>
  <view class="tabbar">
    <view v-for="item in list" :key="item.id" class="bar" @click="switchTab(item)">
      <view class="card" :class="{ active: props.id == item.id }">
        <image class="image" :src="props.id == item.id ? item.selectIcon : item.icon"></image>
        <view class="text">{{ item.text }}</view>
      </view>
    </view>
  </view>
</template>

<script setup lange="ts">
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const url = '../../static/image/tabbar/'
const props = defineProps({
  id: {
    type: Number,
    default: 0
  }
})
const text1 = computed(() => t('tabbar.index'))
const text2 = computed(() => t('tabbar.market'))
const text3 = computed(() => t('tabbar.jiaoyi'))
const text4 = computed(() => t('tabbar.record'))
const text5 = computed(() => t('tabbar.user'))
const list = ref([
  {
    path: '/pages/index/index',
    icon: `${url}index2.png`,
    selectIcon: `${url}index_select2.png`,
    text: text1,
    id: 0
  },
  {
    path: '/pages/market/market',
    icon: `${url}market2.png`,
    selectIcon: `${url}market_select2.png`,
    text: text2,
    id: 1
  },
  {
    path: '/pages/jiaoyi/jiaoyi',
    icon: `${url}jiaoyi2.png`,
    selectIcon: `${url}jiaoyi_select2.png`,
    text: text3,
    id: 2
  },
  {
    path: '/pages/record/record',
    icon: `${url}record2.png`,
    selectIcon: `${url}record_select2.png`,
    text: text4,
    id: 3
  },
  {
    path: '/pages/user/user',
    icon: `${url}user2.png`,
    selectIcon: `${url}user_select2.png`,
    text: text5,
    id: 4
  }
])

const switchTab = (item) => {
  if (item.path) {
    uni.switchTab({
      url: item.path
    })
  }
}
</script>

<style scoped lang="scss">
.isIphoneX {
  padding-bottom: 2.13rem !important;
}
.tabbar {
  width: 100%;
  // height: 5.5rem;
  height: 4.375rem;
  background: #0f0f0f;
  display: flex;
  justify-content: space-around;
  position: fixed;
  z-index: 1000;
  bottom: 0;
  left: 0;
  // border-top: 0.03rem solid #1b3224;
  // box-shadow: ;
  box-shadow: 0 0.53rem 0.85rem 0.13rem #00000060;
  border-radius: 0 0 0 0;
  .bar {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .card {
    width: 3.59rem;
    border-radius: 0.63rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .image {
      width: 1.88rem;
      height: 1.88rem;
      padding: 0.22rem;
    }
    .text {
      font-size: 0.65rem;
      color: #707070;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      word-break: break-all;
    }
  }
  .active {
    .text {
      // color: $color-primary;
      color: #df032f;
    }
  }
}
</style>
