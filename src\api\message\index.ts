import request from '@/utils/http'
import config from '@/utils/config'
import { messageType } from './messageType'

export function getUserMessageApi(params: messageType) {
  return request({
    url: `${config.baseURL}/index/get_user_message`,
    method: 'get',
    params
  })
}
export function getUserMessageDetailApi() {
  return request({
    url: `${config.baseURL}/index/get_user_message_detail`,
    method: 'get'
  })
}
