<template>
  <Navigater :title="t('user.yinhangzhanghu')" />

  <view class="inlet" @click="goPage">
    <image :src="plus" mode="widthFix" />
    <div class="label">{{ t('account.cunquzhanghaodengji') }}</div>
  </view>
  <scroll-view scroll-y :style="{ height: `calc(${store.pageHeight} - 9.8313rem)` }">
    <view class="account-card-list">
      <NotData v-if="!bankList.length" />
      <view v-for="(item, i) in bankList" :key="i" class="account-card">
        <!-- <img src="@/static/image/bank/bg.png" class="bg" /> -->
        <view class="account-info">
          <view class="bank-name"> {{ item.bank_name }} </view>
          <view class="user-name"> {{ item.shiming_name }} </view>
        </view>

        <van-icon class="account-del-icon" name="delete-o" color="#fff" size="20" @click="delBank(item.id)" />

        <view class="bank-account-number">{{ item.bank_num.slice(0, 4) }} **** **** **** </view>
      </view>
    </view>
  </scroll-view>
</template>
<script setup lang="ts">
import { getBankListApi, delBankApi } from '@/api/bankAccount'
import { BankInfoType } from '@/api/bankAccount/indexType'
import { useCounterStore } from '@/store/store'
import { useI18n } from 'vue-i18n'
import { onMounted, Ref, ref } from 'vue'
import { showSuccessToast } from 'vant'
import plus from '@/static/image/market/plus.png'
const { t } = useI18n()
// 页面基础配置
const store = useCounterStore()

type BankListType = [BankInfoType | never]

const bankList: Ref<BankListType> = ref([])

const delBank = async (id: number | string) => {
  const res = await delBankApi(id)

  if (res.code === 1) {
    showSuccessToast(res.data.msg)
    getBankLsit()
  }
}

const getBankLsit = async () => {
  const res = await getBankListApi()
  if (res.code === 1) {
    bankList.value = res.data.user_bankcard
    // showSuccessToast(t('toastText.chenggong'))
  }
  console.log(res, 'bank')
}

onMounted(() => {
  getBankLsit()
})

const goPage = () => {
  uni.redirectTo({ url: '/subPackages/bankAccount/accountLog' })
}
</script>

<style lang="scss" scoped>
* {
  font-weight: 500;
}
body {
  height: 100%;
}
.bg {
  width: 100%;
  // height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.inlet {
  height: 5rem;
  border-radius: 0.5rem;
  margin: 0.8125rem 0.94rem;
  border: 0.05rem solid $color-primary;
  text-align: center;
  image {
    width: 1.31rem;
    height: 1.31rem;
    margin-top: 1.19rem;
  }
  .label {
    font-size: 0.75rem;
    color: #222;
    margin-top: 0.25rem;
  }
}

.account-card-list {
  align-items: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .account-card {
    position: relative;
    width: 21.5625rem;
    height: 8.75rem;
    padding-top: 1.4063rem;
    background: linear-gradient(180deg, #ff1645 0%, #bb0025 100%);

    border-radius: 0.9375rem;
    margin-bottom: 0.8125rem;
    overflow: hidden;
    display: flex;
    justify-content: space-between;

    position: relative;

    .bank-account-number {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3.1563rem;
      color: #fff;
      // background: #fff;
      border-radius: 0 0 0.9375rem 0.9375rem;

      font-weight: 600;
      font-size: 1.875rem;
      line-height: 3.1563rem;
      padding-left: 1.0625rem;
    }

    .account-del-icon {
      margin-right: 1.0625rem;
      width: 0.7813rem;
      height: 0.9063rem;
    }

    .account-info {
      margin-left: 1.0625rem;
      z-index: 9;

      .bank-name {
        font-size: $uni-font-size-1;
        color: #fff;
      }
      .user-name {
        font-size: $uni-font-size-1;
        color: #fff;
      }
    }
  }
}
</style>
