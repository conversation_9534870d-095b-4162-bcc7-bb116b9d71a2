<template>
  <view class="container">
    <!-- <div class="navigator">
      <view class="back" @click="reLaunch('/subPackages/indexLoading/simulation')">
        <van-icon name="arrow" color="#000" size="1.3rem" />
      </view>
    </div> -->

    <view class="wrap">
      <img class="bg" :src="bg" />
      <view class="title">{{ t('login.tip3') }}</view>
      <view class="mas">
        <view class="input_wrap">
          <input v-model="loginParams.account" :placeholder="t('login.account_placeholder')" />
        </view>
        <view class="input_wrap">
          <input v-model="loginParams.password" :placeholder="t('login.password_placeholder')" :type="showPassword ? 'text' : 'password'" />
          <van-icon v-if="showPassword" @click="showPassword = !showPassword" name="eye-o" size="1.3rem" />
          <van-icon v-else @click="showPassword = !showPassword" name="closed-eye" size="1.3rem" />
        </view>
      </view>
      <view class="button-wrap">
        <view class="button" @click="login">{{ t('login.button_text') }}</view>
        <view class="text" @click="reLaunch('/subPackages/register/register')">{{ t('login.register') }}</view>
        <view class="button circle_button" @click="reLaunch('/subPackages/register/register')">{{ t('register.tip4') }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { loginApi } from '@/api/login/login'
import { switchTab, reLaunch, checkInput } from '@/common/common'
import { useCounterStore } from '@/store/store'
import { showSuccessToast } from 'vant'
import { getCzhiurlApi } from '@/api/user'
import bg from '@/static/logo2.png'
import { onLoad } from '@dcloudio/uni-app'
const { t } = useI18n()
const store = useCounterStore()

const loginParams = ref({
  account: '',
  password: ''
})
const showPassword = ref(false)

const kefu_url = ref('')
onLoad(() => {
  getCzhiurlFn()
})

const getCzhiurlFn = async () => {
  const res = await getCzhiurlApi()
  if (res.code === 1) {
    kefu_url.value = res.data.kefu_url
  }
}

const checkArr = [
  { key: 'account', message: t('login.account_error') },
  { key: 'password', message: t('login.password_error') }
]

const login = async () => {
  if (!checkInput(checkArr, loginParams.value)) {
    return
  }
  uni.showLoading({ mask: true })
  const res = await loginApi(loginParams.value)
  uni.hideLoading()
  if (res.code === 1) {
    uni.setStorageSync('token', res.data.userinfo.token)
    uni.setStorageSync('userId', res.data.userinfo.id)
    uni.setStorageSync('userInfo', res.data.userinfo)
    store.token = res.data.userinfo.token
    store.userId = res.data.userinfo.userId
    store.getUserInfo()
    showSuccessToast(res.msg)
    setTimeout(() => {
      switchTab('/pages/index/index')
    }, 1000)
  }
}
</script>

<style lang="scss" scoped>
.navigator {
  width: 100%;
  height: 3.13rem;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 1rem;
  .van-icon {
    transform: rotate(180deg);
  }
}

body {
  height: 100vh;
  background: #f5f5f5;
}

.wrap {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: calc(var(--vh) * 100);
}
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  .bg {
    width: 8.13rem;
    height: 8.13rem;
    margin: 1.44rem auto 0;
    border-radius: 0.31rem;
    transform: scale(1.5);
  }
  .title {
    color: $color-black;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 2rem 0 1.88rem;
  }
}
.name {
  margin: 2.3vh auto 9.54vh;
  font-size: 1.5rem;
  color: $uni-color-primary;
}
.button-wrap {
  width: 20rem;
  margin-top: 1.875rem;
  .text {
    color: #4a5567;
    font-size: 0.9375rem;
    line-height: 3.125rem;
  }
  .button {
    border-radius: 3.125rem;
    // background: linear-gradient(0deg, #e43e17 0%, #f7c54f 100%);
    background: $color-primary;
    width: 100%;
    font-weight: 500;
  }
}
.mas {
  width: 20rem;
  height: 7.375rem;
  border-radius: 1.25rem;
}
.input_wrap {
  display: flex;
  align-items: center;
  height: 3.06rem;
  border-radius: 0.75rem;
  margin-bottom: 1.25rem;
  background: #fff;
  padding: 0 0.88rem;
  max-width: 20rem;
  margin-left: auto;
  margin-right: auto;
  font-size: 1rem;
  .icon {
    width: 1.1875rem;
    height: 1.1875rem;
    margin-right: 0.94rem;
    image {
      width: 100%;
      height: 100%;
      display: block;
    }
  }
  input {
    flex: 1;
    text-align: left;
    color: #0b121b;
    .uni-input-placeholder {
      color: #afafaf;
    }
  }
}
.text_button {
  max-width: 20rem;
  font-size: $uni-font-size-1;
  color: $uni-color-primary;
  display: flex;
  justify-content: space-between;
  margin: 0.31rem auto;
  view {
    padding: 0.31rem;
  }
}
.button {
  width: 11.25rem;
  height: 3.75rem;
  background-color: $uni-color-primary;
  border-radius: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
}
.circle_button {
  border: 0.06rem solid #e3e8ef;
  background: transparent !important;
  color: $color-primary;
}
</style>
