<template>
  <view class="container">
    <!-- 检索栏 -->
    <view class="search">
      <view class="search-box hh" @click="goPage('/subPackages/search/search')">
        <image src="/static/image/index/search.png" class="search-icon"></image>
        <span>{{ t('search.tip3') }}</span>
      </view>
      <van-badge :dot="dot" style="display: flex">
        <image src="/static/image/index/tishi.png" class="xiaoxi" mode="widthFix" @click="goPage('/subPackages/message/message')"></image>
      </van-badge>
      <!-- <image src="/static/image/index/icon_9.png" class="kefu" @click="goWeb()"></image> -->
    </view>

    <!-- 主体 -->
    <view class="content">
      <!-- 股票 -->
      <div class="card">
        <div class="left">
          <div class="flex">
            <div class="price">{{ activeGupiao.price }}</div>
            <div class="title" @click="openSelect">
              <span>{{ activeGupiao?.name || '' }}</span>
              <van-icon name="arrow-down" color="#101828"></van-icon>
            </div>
          </div>
          <div class="info">
            <div class="label" :style="{ color: getColor(activeGupiao) == 'up' ? '#EA3522' : '#31CC43' }">{{ activeGupiao?.zhangdieshu }}</div>
            <div class="rate" :style="{ color: getColor(activeGupiao) == 'up' ? '#EA3522' : '#31CC43' }">{{ `(${activeGupiao?.zhangdiebaifenbi})%` }}</div>
          </div>
        </div>
        <div class="right">
          <GupiaoChart :value="chartValue" height="9.25rem" />
        </div>
      </div>

      <view class="buttonList">
        <view v-for="(item, index) in buttonList" :key="index" class="button" @click="goPage2(item)">
          <div class="image-box">
            <image :src="item.icon"></image>
          </div>
          <view class="text">{{ item.label }}</view>
        </view>
      </view>

      <view class="scroll_list">
        <view class="title-top" @click="switchTab('/pages/market/market')">
          <view class="title h">{{ t('index.hot') }}</view>
          <view class="title-right"
            >{{ t('index.tip2') }}
            <van-icon name="arrow"></van-icon>
          </view>
        </view>
        <view class="gupiao_wrap">
          <view v-for="(item, index) in hotGupiaoList" :key="index" class="gupiao" @click="goPage(`/subPackages/index/gupiaoDetail/gupiaoDetail?id=${item.id}`)">
            <view class="flex items-center justify-between">
              <view class="">
                <view class="name">{{ item.name }}</view>
                <!-- &nbsp;&nbsp; -->
                <view class="daima">{{ item.shuzidaima }}</view>
              </view>
              <view>
                <view class="text-primary text-lg">
                  <span class="price" :class="getColor(item)">{{ changeMoney(item.price) }}</span>
                  <span v-if="item.zhangdiebaifenbi === 0"> - </span>
                  <van-icon v-else :class="getColor(item, true)" name="down" size=".75rem" />
                </view>
                <view class="flex items-center justify-between mt-[0.28rem]">
                  <view class="text-base text-right" style="font-size: 0.69rem" :class="getColor(item)">{{ item.zhangdieshu }}</view>
                  <view class="text-base text-right" style="font-size: 0.69rem" :class="getColor(item)">（{{ item.zhangdiebaifenbi }}%）</view>
                </view>
              </view>
            </view>

            <!-- <div class="bottom">
              <div class="image">
                <image :src="item.zhangdiebaifenbi > 0 ? up : item.zhangdiebaifenbi < 0 ? down : ''" mode="widthFix" />
              </div>
            </div> -->
          </view>
        </view>
      </view>
    </view>

    <van-popup v-model:show="selectShow" class="select_window" v-bind="windowOptions">
      <view v-for="(item, index) in hotGupiaoList.slice(0, 3)" :key="index" class="title" @click="handleSelect(index)">{{ item.name }}</view>
    </van-popup>
  </view>
  <CustomTabbar :id="0" />
</template>

<script lang="ts" setup>
import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'
import GupiaoChart from './components/gupiaoChart.vue'
import { useCounterStore } from '@/store/store'
import { getTickerKApi, getHotGupiaoApi } from '@/api/index/index'
import { getUserMessageApi } from '@/api/message'
import { onHide, onShow } from '@dcloudio/uni-app'
import { ref, watch, computed } from 'vue'
import { getCzhiurlApi } from '@/api/user'
import { useI18n } from 'vue-i18n'
import { goPage, getColor, switchTab, changeMoney } from '@/common/common'
// import up from '@/static/image/index/up.png'
// import down from '@/static/image/index/down.png'
const { t } = useI18n()
// 页面基础配置
const store = useCounterStore()
console.log(store)
const dot = ref(false)
// 获取消息
const getUserMessage = async () => {
  const res = await getUserMessageApi()
  if (res.code === 1) {
    dot.value = res.data.data[0]?.is_read === '0'
  }
}

const kefu_url = ref('')

const getCzhiurlFn = async () => {
  const res = await getCzhiurlApi()
  if (res.code === 1) {
    kefu_url.value = res.data.kefu_url
  }
}

const goWeb = () => {
  window.open(kefu_url.value)
}

const goPage2 = (e) => {
  if (e.type === 'kefu') {
    goWeb()
  } else {
    goPage(e.url)
  }
}

let inId: any = null
onShow(() => {
  getHotGupiao()
  getUserMessage()
  getCzhiurlFn()

  inId = setInterval(() => {
    // 获取热门股票
    getHotGupiao()
    getUserMessage()
  }, 10000)
})

onHide(() => {
  console.log(inId, 'iddd')

  clearInterval(inId)
})
// const button1 = computed(() => t('index.button1'))
// const button2 = computed(() => t('dadan.title'))
// const button4 = computed(() => t('index.button4'))
// const button6 = computed(() => t('hongli.title'))
const buttonList = [
  { label: t('index.button1'), icon: '/static/image/index/imgs1.png', url: '/subPackages/index/ipo/ipo' },
  { label: t('hongli.title'), icon: '/static/image/index/imgs2.png', url: '/subPackages/index/hongli/hongli' },
  { label: t('dadan.title'), icon: '/static/image/index/imgs3.png', url: '/subPackages/index/dadan/dadan' },
  { label: t('index.button4'), icon: '/static/image/index/imgs5.png', url: '/subPackages/transactionLog/transactionLog?isLs=1' },
  { label: t('rongzi.title'), icon: '/static/image/index/imgs4.png', url: '/subPackages/index/rongzi/rongzi' },
  { label: t('kefu.title'), icon: '/static/image/index/imgs6.png', type: 'kefu' }
]
console.log(buttonList)

const gupiaoActive = ref(0)
const chartValue = ref({})

// 熱門股票
interface hotGupiaoType {
  name: string
  shuzidaima: string
  price: string
  zhangdiebaifenbi: number | string
  zhangdieshu: number | string
  id: number
}
const hotGupiaoList = ref<Array<hotGupiaoType>>([])
const activeGupiao = computed(() => {
  return hotGupiaoList.value[gupiaoActive.value] || { zhangdiebaifenbi: 0 }
})
const getHotGupiao = async () => {
  const res = await getHotGupiaoApi()
  hotGupiaoList.value = res.data.result
}

// 股票chart
watch(
  () => hotGupiaoList,
  (newData) => {
    resetChartData(newData.value[gupiaoActive.value])
  },
  { deep: true }
)
const resetChartData = async (data: any) => {
  console.log(111, data)
  const params = {
    id: data.id,
    kline_type: 6,
    type: 1
  }
  const res = await getTickerKApi(params)
  const kLineList = res.data.map((item: any) => {
    return {
      close: item.c,
      high: item.h,
      low: item.l,
      open: item.o,
      timestamp: item.t * 1000,
      volume: item.v
    }
  })
  chartValue.value = kLineList
}

// 打开筛选框
const selectShow = ref(false)
const windowOptions = {
  position: 'bottom',
  'close-on-click-overlay': true,
  'safe-area-inset-bottom': true
}
const openSelect = () => {
  selectShow.value = true
}
const handleSelect = (index: number) => {
  gupiaoActive.value = index
  resetChartData(hotGupiaoList.value[gupiaoActive.value])
  selectShow.value = false
}
</script>
<style scoped lang="scss">
.container {
  overflow: hidden;
  height: calc(var(--vh) * 100 - 4.375rem);
}

.h {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    width: 0%;
    height: 0.19rem;
    background: #0df69e;
    border-radius: 0.25rem;
    bottom: -0.31rem;
    opacity: 0;
    transition: all 1s;
  }
  &:hover {
    &::before {
      width: 100%;
      opacity: 1;
    }
  }
}

.search {
  width: 100%;
  height: 3.125rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem 0.625rem 0.9375rem;
  gap: 1.25rem;
  margin: 0.53rem 0;
  .touxian {
    width: 1.7813rem;
    height: 1.7813rem;
    border-radius: 50%;
    margin-right: 0.9375rem;
  }

  .search-box {
    width: 14.375rem;
    flex: 1;
    height: 2.75rem;
    border-radius: 1.25rem;
    background: #fff;
    line-height: 2.75rem;
    padding: 0 0.94rem;
    font-size: 0.9375rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    .search-icon {
      width: 1rem;
      height: 1rem;
    }
    span {
      color: #aeaeae;
      font-size: 0.82rem;
    }
  }

  .kefu {
    width: 1rem;
    height: 1rem;
  }

  .xiaoxi {
    width: 1.13rem;
  }
}

.content {
  height: calc(var(--vh) * 100 - 4.38rem - 3.125rem - 1.06rem);
  overflow-y: auto;
  .card {
    margin: 0 0.94rem 1.25rem;
    border-radius: 0.94rem;
    padding: 1rem 0;
    background: #f5f5f5;
    > div {
      flex: 1;
    }

    .left {
      padding: 0 1rem;
      .flex {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .title {
        height: 1.875rem;
        line-height: 1.875rem;
        font-size: 0.9375rem;
        font-weight: 600;
        background: $color-primary;
        padding: 0 0.31rem;
        border-radius: 0.75rem;
        // background: linear-gradient(180deg, #00d3b7 0%, #019682 100%);
        // background: #3185fe;
        border-radius: 1.25rem;
        background: #ffffff;
        span {
          color: #101828;
          margin-right: 0.64rem;
        }
      }
      .price {
        font-size: 1.5625rem;
        font-weight: bold;
      }
      .info {
        display: flex;
        gap: 0.31rem;
        align-items: center;
      }
      .rate {
        padding: 0.19rem;
        border-radius: 0.31rem;
        font-size: 0.81rem;
      }
      .label {
        font-size: 0.81rem;
      }
    }
    .right {
      padding: 0 1rem;
    }
  }

  .buttonList {
    margin: 1.25rem 0 1.25rem;
    padding: 0 0.9375rem;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 0.63rem 0;
    .button {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 30%;
      height: 5.625rem;
      padding: 0.625rem;
    }
    .image-box {
      width: 1.5625rem;
      height: 1.5rem;
      display: flex;
      justify-content: center;
      align-items: center;
      // border-radius: 50%;
      // overflow: hidden;

      // background: #f3f4f6;
      // filter: drop-shadow(0px 3px 7.6px rgba(0, 0, 0, 0.2));
      // border: 1px solid #fff;
    }
    image {
      width: 1.5625rem;
      height: 1.5rem;
      // border-radius: 50%;
    }
    .text {
      color: $color-black;
      margin-top: 0.44rem;
      font-size: 0.75rem;
      width: 100%;
      text-align: center;
    }
  }

  .scroll_list {
    width: 100%;
    padding: 0.3rem 0.9375rem 1.3rem;
    .gupiao_wrap {
      // display: flex;
      // flex-wrap: wrap;
      justify-content: space-between;
      margin-top: 0.625rem;

      .gupiao {
        width: 100%;
        // height: 6.1875rem;
        border-radius: 0.625rem;
        padding: 0.94rem 0.94rem 0.9375rem 0.94rem;
        margin-bottom: 0.625rem;
        border-radius: 0.9375rem;
        border: 1px solid #eaecf0;
        background: #fff;

        .price {
          font-size: 1.0625rem;
          font-weight: 500;
        }

        .name {
          color: $color-black;
          font-size: 0.875rem;
          font-weight: 500;
          max-height: 6.25rem;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          word-break: break-all;
        }
        .daima {
          color: $color-black;
          font-size: 0.875rem;
          font-weight: 500;
        }

        .text-lg {
          display: flex;
          align-items: center;
          justify-content: flex-end;
        }
      }

      .bottom {
        display: flex;
        justify-content: space-between;
        image {
          margin-top: 0.5rem;
          width: 19.9688rem;
        }
      }
    }

    .title-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .title {
      color: $color-black;
      font-size: 0.875rem;
      font-weight: 400;
    }

    .title-right {
      display: flex;
      align-items: center;
      // color: $color-primary;
      font-size: 0.875rem;
      font-weight: 400;
      img {
        height: 0.7383rem;
        width: 0.4375rem;
        margin-left: 0.4063rem;
      }
    }
  }

  .wrap {
    margin-top: 1.4063rem;
    > .title {
      height: 2.38rem;
      padding-top: 1.06rem;
      font-size: $uni-font-size-1;
      font-weight: bold;
      margin-left: 0.47rem;
    }
    .button_wrap {
      display: grid;
      grid-template-columns: 50% 50%;
      grid-template-rows: 2.69rem 2.69rem 2.69rem 2.69rem;
      row-gap: 0.25rem;
      column-gap: 0.44rem;
      .button {
        background-color: #638daa;
        border-radius: 0.56rem;
        display: flex;
        justify-content: center;
        align-items: center;
        image {
          width: 0.94rem;
          height: 0.94rem;

          margin-right: 0.47rem;
        }
        view {
          color: #fff;
          width: 6.75rem;
          font-size: 0.63rem;
        }
      }
    }
  }
}
.select_window {
  padding: 0.51rem 1rem 0.9rem;
  .title {
    height: 2.5rem;
    line-height: 2.5rem;
    font-size: $uni-font-size-lg;
    padding: 0 1rem;
    text-align: center;
    border-bottom: 0.03rem solid #ccc;
  }
}
</style>
