import axios from 'axios'
import { showFailToast } from 'vant'

// 创建axios实例
const service = axios.create({
  timeout: 5000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    console.log(config)
    const token = uni.getStorageSync('token')
    config.headers.token = token

    if (uni.getStorageSync('locale') === '' || !uni.getStorageSync('locale')) {
      uni.setStorageSync('locale', 'jp')
    }
    if (config?.params) {
      config.params.lang = uni.getStorageSync('locale')
    } else {
      config.params = { lang: uni.getStorageSync('locale') }
    }

    return config
  },
  (error) => {
    // 请求错误处理
    console.log(error) // for debug
    Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    // 对响应数据做处理，例如只返回data部分
    const res = response.data

    // 根据业务判断是否需要进行错误处理
    if (res.code !== 1) {
      showFailToast(res.msg)
    }
    return res
  },
  (error) => {
    const res = error.response.data
    // token 过期处理
    if (res.code === 401) {
      console.log('token 过期处理')

      // 清除token 以及用户信息
      uni.setStorageSync('token', null)
      uni.setStorageSync('userId', null)
      uni.setStorageSync('userInfo', null)

      // 提示用户
      // showFailToast('ユーザー情報が無効になりました。再ログインしてください')
      // uni.showToast({
      //   title: 'ユーザー情報が無効になりました。再ログインしてください',
      //   icon: 'none'
      // })

      setTimeout(() => {
        // 跳转登录
        uni.navigateTo({
          url: '/subPackages/login/login'
        })
      }, 1500)

      return Promise.reject(error)
    }

    // 响应错误处理
    console.log('err' + error) // for debug
    // showFailToast(error.msg || 'error')
    showFailToast({
      duration: 2000,
      message: error.msg || 'error'
    })
    // uni.showToast({
    //   title: error.msg || 'error',
    //   icon: 'none'
    // })
    return Promise.reject(error)
  }
)

export default service
