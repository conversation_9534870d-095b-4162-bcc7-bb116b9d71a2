<script lang="ts" setup>
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const goPage = (url: string) => {
  uni.navigateTo({
    url
  })
}
</script>

<template>
  <view class="box">
    <!-- <image src="/src/static/logo.jpg" class="logo"></image> -->
    <view class="btn1" @click="goPage('/subPackages/register/register')">{{ t('logout.registertip') }}</view>
    <view class="btn2" @click="goPage('/subPackages/login/login')">{{ t('logout.logotip') }}</view>
  </view>
</template>

<style lang="scss" scoped>
uni-page-body {
  width: 100%;
  height: 100%;
  background: #ffffff;
}
.box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .name {
    margin: 2.3vh auto 0;
    font-size: 1.5rem;
    color: $uni-color-primary;
  }
  .logo {
    width: 7.5rem;
    height: 7.5rem;
    // border-radius: 50%;
    //  background-color: $uni-color-primary;
    margin: 14vh auto 0;
  }

  .btn1 {
    width: 19.7813rem;
    height: 2.6563rem;
    margin-top: 3.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ee2560;
    border-radius: 1.3438rem;

    font-weight: 300;
    font-size: 1rem;
    color: #ffffff;
  }
  .btn2 {
    width: 19.7813rem;
    height: 2.6563rem;
    margin-top: 1.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #08182b;
    // border: 1px solid #50aff0;
    border-radius: 1.3438rem;

    font-weight: 300;
    font-size: 1rem;
    color: #fff;
  }
}
</style>
