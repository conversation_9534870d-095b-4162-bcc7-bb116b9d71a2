<template>
  <view class="container">
    <!-- <div class="navigator">
      <view class="back" @click="reLaunch('/subPackages/indexLoading/simulation')">
        <van-icon name="arrow" color="#000" size="1.3rem" />
      </view>
    </div> -->
    <view class="wrap">
      <view class="title">{{ t('register.tip4') }}</view>
      <view class="mas">
        <view class="input_wrap">
          <input v-model="registerParams.account" :placeholder="t('register.account_placeholder')" />
        </view>
        <view class="input_wrap">
          <input v-model="registerParams.password" :placeholder="t('register.password_placeholder')" :type="showPassword ? 'text' : 'password'" />
          <van-icon v-if="showPassword" @click="showPassword = !showPassword" name="eye-o" size="1.3rem" />
          <van-icon v-else @click="showPassword = !showPassword" name="closed-eye" size="1.3rem" />
        </view>
        <view class="input_wrap">
          <input v-model="registerParams.again_password" :placeholder="t('register.password_again_placeholder')" :type="showPassword2 ? 'text' : 'password'" />
          <van-icon v-if="showPassword2" @click="showPassword2 = !showPassword2" name="eye-o" size="1.3rem" />
          <van-icon v-else @click="showPassword2 = !showPassword2" name="closed-eye" size="1.3rem" />
        </view>
        <view class="input_wrap">
          <input v-model="registerParams.yaoqingma" :placeholder="t('register.code_placeholder')" />
        </view>
      </view>
      <view class="button-wrap">
        <view class="button" @click="register">{{ t('register.tip4') }}</view>
        <!-- <view class="text" @click="reLaunch('/subPackages/login/login')">{{ t('register.tip3') }}</view> -->
        <view class="button circle_button" @click="reLaunch('/subPackages/login/login')">{{ t('login.button_text') }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { registerApi } from '@/api/login/login'
import { reLaunch, checkInput, switchTab } from '@/common/common'
import { showFailToast, showLoadingToast, showSuccessToast, showToast } from 'vant'
import { useCounterStore } from '@/store/store'
const { t } = useI18n()
const store = useCounterStore()

const registerParams = ref({
  account: '',
  password: '',
  again_password: '',
  yaoqingma: ''
})

const showPassword = ref(false)
const showPassword2 = ref(false)
const checkArr = [
  { key: 'account', message: t('register.account_error') },
  { key: 'password', message: t('register.password_error') },
  { key: 'again_password', message: t('register.password_again_error') },
  { key: 'yaoqingma', message: t('register.code_error') }
]

const register = async () => {
  const reg = /^(070|080|090)\d{8}$/
  if (!checkInput(checkArr, registerParams.value)) {
    return
  }
  if (!reg.test(registerParams.value.account)) {
    return showToast(t('register.tip1'))
  }

  if (registerParams.value.password.length < 6 || registerParams.value.password.length > 10) {
    return showToast(t('register.tip2'))
  }

  if (registerParams.value.password !== registerParams.value.again_password) {
    showFailToast(t('register.password_repeat_error'))
    return
  }
  showLoadingToast({ mask: true })
  const res = await registerApi(registerParams.value)
  // closeToast()
  if (res.code === 1) {
    uni.setStorageSync('token', res.data.userinfo.token)
    uni.setStorageSync('userId', res.data.userinfo.id)
    uni.setStorageSync('userInfo', res.data.userinfo)
    store.token = res.data.userinfo.token
    store.userId = res.data.userinfo.userId
    store.getUserInfo()
    showSuccessToast(res.msg)
    setTimeout(() => {
      switchTab('/pages/index/index')
    }, 1000)
  } else {
    showFailToast(res.msg)
  }
}
</script>

<style lang="scss" scoped>
.navigator {
  width: 100%;
  height: 3.13rem;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 1rem;
  .van-icon {
    transform: rotate(180deg);
  }
}
.button-wrap {
  width: 20rem;
  height: 3.125rem;
  justify-content: space-between;
  align-content: center;
  margin-top: 0.625rem;
  .text {
    color: #a6a6a6;
    font-size: 0.9375rem;
    line-height: 3.125rem;
  }
}
.mas {
  width: 20rem;
  border-radius: 1.25rem;
}
body {
  background: #f5f5f5;
}
.wrap {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.container {
  text-align: center;
  margin: 0 auto;
  .bg {
    width: 15.7188rem;
    height: 12.8422rem;
    margin: 0.5625rem auto 0;
  }
  .title {
    color: $color-black;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 5.25rem 0 1.75rem;
  }
}
.logo {
  width: 7.5rem;
  height: 7.5rem;
  margin: 14vh auto 0vh;
}
.name {
  margin: 0vh auto 9.54vh;
  font-size: 1.5rem;
  color: $uni-color-primary;
}
.input_wrap {
  display: flex;
  align-items: center;
  height: 3.06rem;
  border-radius: 0.94rem;
  background: #fff;
  margin-bottom: 0.94rem;
  padding: 0 0.88rem;
  max-width: 20rem;
  margin-left: auto;
  margin-right: auto;
  font-size: 1rem;
  .icon {
    width: 1.1875rem;
    height: 1.1875rem;
    margin-right: 0.94rem;
    image {
      width: 100%;
      height: 100%;
      display: block;
    }
  }
  input {
    flex: 1;
    text-align: left;
    color: #0b121b;
    .uni-input-placeholder {
      color: #afafaf;
    }
  }
}
.button {
  width: 100%;
  height: 3.75rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  border-radius: 3.125rem;
  // background: linear-gradient(0deg, #e43e17 0%, #f7c54f 100%);
  background: $color-primary;
  font-weight: 500;
}
.circle_button {
  border: 0.06rem solid #e3e8ef;
  background: transparent !important;
  margin-top: 1.25rem;
  color: $color-primary;
}
</style>
