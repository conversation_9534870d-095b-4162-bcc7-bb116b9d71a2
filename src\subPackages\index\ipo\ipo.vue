<template>
  <Navigater :title="t('ipo.title')" />
  <view class="button_group">
    <view class="button" :class="{ active: pageType === 0 }" @click="changePageType(0)">{{ t('ipo.button1') }}</view>
    <view class="button" :class="{ active: pageType === 1 }" @click="changePageType(1)">{{ t('ipo.button2') }}</view>
  </view>

  <scroll-view scroll-y :style="{ height: `calc(var(--vh) * 100 - 3.13rem - 2.75rem)` }">
    <view class="gupiao_list">
      <view v-show="pageType === 0">
        <view v-for="(item, index) in shengoulist" :key="index" class="gupiao1" @click="openWindow(item)">
          <div class="top">
            <div class="title">{{ item?.name }}</div>
            <div class="daima">{{ item?.shuzidaima }}</div>
            <div class="date">
              <!-- <span>{{ t('ipo.shijian') }}：</span> -->
              <span>{{ t('jiaoyi.chouqianri') }}：</span>
              <span>{{ item.chouqiandate }}</span>
            </div>
          </div>
          <div class="bottom">
            <div class="left">
              <div class="row">
                <div class="label">{{ t('ipo.jiage') }}：</div>
                <div class="value price">{{ changeMoney(item.chengxiaojia) }}</div>
              </div>
              <div class="row">
                <div class="label">{{ t('ipo.shuliang') }}：</div>
                <div class="value">1000000</div>
              </div>
            </div>
            <div class="right">{{ t('ipo.button1') }}</div>
          </div>
        </view>
        <NotData v-if="shengoulist.length === 0"></NotData>
      </view>
      <view v-show="pageType === 1">
        <NotData v-if="xinguApply.length === 0"></NotData>

        <view v-for="(item, index) in xinguApply" :key="index" class="gupiao2">
          <div class="top">
            <div class="flex">
              <div class="title">{{ item?.product?.name }}</div>
              <div class="daima">{{ item?.product?.shuzidaima }}</div>
            </div>
            <div class="date">
              <span>{{ t('jiaoyi.chouqianri') }}：</span>
              <span>{{ item?.product.chouqiandate }}</span>
            </div>
          </div>

          <div class="row">
            <div class="left">
              <view class="label">{{ t('jiaoyi.jiage') }}</view>
              <view class="value red">{{ changeMoney(item.price) }}</view>
            </div>
            <div class="right">
              <view class="label">{{ t('jiaoyi.zhognqianshuliang') }}</view>
              <view class="value">{{ item.zhongqianshu }}</view>
            </div>
          </div>

          <div class="row">
            <div class="left">
              <view class="label">{{ t('jiaoyi.shenqingliang') }}</view>
              <view class="value">{{ item.shengoushuliang }}</view>
            </div>
            <div class="right">
              <view class="label">{{ t('jiaoyi.renjiaogushu') }}</view>
              <view class="value">{{ Number(item.renjiaonum) || 0 }}</view>
            </div>
          </div>

          <div class="row">
            <div class="left">
              <view class="label">{{ t('jiaoyi.txt1') }}</view>
              <view class="value">{{ changeMoney(item.yingrenjiao) }}</view>
            </div>
            <div class="right">
              <view class="label">{{ t('jiaoyi.txt2') }}</view>
              <view class="value">{{ changeMoney(item.yirenjiao) }}</view>
            </div>
          </div>

          <div class="button">{{ getText(item) }}</div>
        </view>
      </view>
    </view>
  </scroll-view>

  <van-popup v-model:show="gupiaoWindowShow" class="buy_window" v-bind="windowOptions">
    <div class="head">
      <view class="title">{{ windowDetail.name }}</view>
      <div class="daima">{{ windowDetail.shuzidaima }}</div>
    </div>

    <view class="row">
      <view class="label">{{ t('ipo.windowLabel1') }}</view>
      <view class="value">{{ changeMoney(windowDetail.chengxiaojia) }}</view>
    </view>
    <view class="row">
      <view class="label">{{ t('ipo.windowLabel2') }}（{{ t('ipo.zang') }}）</view>
      <van-stepper v-model="shengouParams.shuliang" step="1" />
    </view>
    <view class="button" @click="shengou">{{ t('gupiaoDetail.submit') }}</view>
    <image class="close" src="/static/close.png" @click="gupiaoWindowShow = false"></image>
  </van-popup>
</template>

<script setup lang="ts">
import { getXinguListApi, shengouApi, getXinguApplyApi } from '@/api/index/index'
import { onHide, onShow } from '@dcloudio/uni-app'
import { useI18n } from 'vue-i18n'
import { ref } from 'vue'
import { changeMoney } from '@/common/common'
import { showLoadingToast, showToast } from 'vant'
const { t } = useI18n()

let id: any = null

onShow(() => {
  getXinguList()
  getXinguApply()
  id = setInterval(() => {
    getXinguApply()
  }, 10000)
})

onHide(() => {
  clearInterval(id)
})

const pageType = ref(0)
const changePageType = async (e: number) => {
  pageType.value = e
}

const getText = (item) => {
  if (item.status === '0') {
    return t('jiaoyi.txt3')
  } else if (item.status === '1' && item.is_examine === 0) {
    return t('jiaoyi.txt4')
  } else if (item.status === '1' && item.is_examine === 1) {
    return t('jiaoyi.txt5')
  } else if (item.status === '1' && item.is_examine === 2) {
    return t('jiaoyi.txt6')
  } else if (item.status === '2') {
    return t('jiaoyi.txt6')
  }
}

const shengoulist = ref([])
const jieshulist = ref([])
const xinguApply = ref([])
const getXinguApply = async () => {
  const res = await getXinguApplyApi()
  if (res.code === 1) {
    xinguApply.value = res.data
    console.log(res, 'res')
  }
}

const getXinguList = async () => {
  const res = await getXinguListApi()
  shengoulist.value = res.data.data.filter((item: any) => item.status === '1')
  jieshulist.value = res.data.data.filter((item: any) => item.status === '2')
  console.log(jieshulist.value, 'jieshulist.value')

  console.log(shengoulist.value)
}

const gupiaoWindowShow = ref(false)
const windowOptions = {
  position: 'bottom',
  'close-on-click-overlay': true,
  'safe-area-inset-bottom': true,
  round: true
}

const windowDetail = ref({})
const openWindow = (e: any) => {
  windowDetail.value = e
  console.log(windowDetail.value)
  gupiaoWindowShow.value = true
}

const shengouParams = ref({
  price: '',
  shuliang: 1000,
  id: ''
})
const shengou = async () => {
  showLoadingToast({ forbidClick: true, duration: 0 })
  shengouParams.value.id = windowDetail.value.id
  shengouParams.value.price = windowDetail.value.chengxiaojia
  const res = await shengouApi(shengouParams.value)
  getXinguApply()
  gupiaoWindowShow.value = false
  showToast({
    message: res.data.msg
  })
}
</script>

<style scoped lang="scss">
.info-box {
  display: flex;
  width: 20.9375rem;
  height: 2.3125rem;
  border-radius: 0.1563rem;
  background: rgb(11, 33, 58, 0.8);
  padding-left: 0.625rem;
  .row {
    flex: 0.8;
    align-items: end;
    justify-content: flex-start;
  }
}

.van-field {
  width: 50%;

  border: 0.03rem solid $uni-color-primary;
  padding: 0.12rem 0.5rem;
  border-radius: 0.16rem;
}
.ul {
  background-color: #fff;
  margin-top: 0.88rem;
  .li {
    height: 2rem;
    padding: 0 1.09rem 0 1.25rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    image {
      width: 0.94rem;
      height: 0.94rem;
      margin-right: 0.63rem;
    }
  }
  .li + .li {
    border-top: 0.03rem solid $uni-text-color;
  }
}
.button_group {
  height: 2.75rem;
  margin: 0 0.94rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.31rem;
  .button {
    width: 50%;
    height: 2.75rem;
    border-radius: 0.7rem;
    font-size: 0.88rem;
    color: $color-primary;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0.05rem solid #e3e8ef;
    color: $color-primary;
  }
  .active {
    background: $color-primary;
    border: 0.05rem solid $color-primary;
    color: #fff;
    font-weight: 500;
  }
}

.gupiao_list {
  padding: 1.25rem 0;
  .gupiao1 {
    margin: 0 1rem;
    padding: 0.5rem 0.31rem;
    border-bottom: 0.06rem solid #14141433;
    margin-top: 0.63rem;
    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        width: 7rem;
        // max-width: 5.5rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        font-size: 0.94rem;
        font-weight: 500;
        color: $color-black;
      }
      .daima {
        height: 1.19rem;
        background: $color-primary;
        border-radius: 0.31rem;
        color: #fff;
        font-size: 0.75rem;
        padding: 0 0.38rem;
        display: flex;
        align-items: center;
      }
      .date {
        span {
          color: #afafaf;
          font-size: 0.75rem;
        }
      }
    }
    .bottom {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      .row {
        display: flex;
        margin-top: 0.56rem;
        align-items: center;
        .label {
          font-size: 0.81rem;
          color: $color-gray;
        }
        .value {
          font-size: 0.81rem;
          color: $color-black;
        }
        .price {
          color: $color-red;
        }
      }
      .right {
        width: 7.5rem;
        height: 2.13rem;
        background: $color-primary;
        border-radius: 0.38rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        color: #fff;
        font-weight: 500;
      }
    }
  }

  .gupiao2 {
    margin: 0 0.5rem;
    padding: 0.5rem 0;
    margin-top: 0.63rem;
    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .flex {
        gap: 1rem;
      }
      .title {
        max-width: 5.5rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        font-size: 0.94rem;
        font-weight: 500;
        color: $color-black;
      }
      .daima {
        height: 1.19rem;
        background: $color-primary;
        border-radius: 0.31rem;
        color: #fff;
        font-size: 0.75rem;
        padding: 0 0.38rem;
        display: flex;
        align-items: center;
      }
      .date {
        span {
          color: #afafaf;
          font-size: 0.75rem;
        }
      }
    }
    .row {
      display: flex;
      justify-content: space-between;
      height: 1.13rem;
      align-items: center;
      margin-top: 0.63rem;
      .left,
      .right {
        display: flex;
        .label {
          color: $color-gray;
          font-size: 0.81rem;
          &::after {
            content: '：';
          }
        }
        .value {
          color: $color-black;
          font-size: 0.81rem;
        }
      }
    }
    .button {
      height: 2.13rem;
      background: $color-primary;
      border-radius: 0.38rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.75rem;
      margin-top: 0.63rem;
      color: #fff;
      font-weight: 500;
    }
  }
}

.buy_window {
  background-color: #fff;
  height: 15.13rem;
  padding: 1.88rem 1.25rem;
  .close {
    position: absolute;
    width: 2.75rem;
    height: 2.75rem;
    padding: 1rem;
    right: 0.5rem;
    top: 0.5rem;
  }
  .head {
    display: flex;
    gap: 1.25rem;
    align-items: center;
    padding: 0 0.31rem;
    position: relative;
    .title {
      font-size: 1rem;
      color: #141414;
    }
    .daima {
      height: 1.19rem;
      background: $color-primary;
      border-radius: 0.31rem;
      font-size: 0.75rem;
      color: #fff;
      padding: 0 0.31rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 0.31rem 0;
    color: #fff;
    .value {
      color: $color-red;
      font-size: 0.88rem;
    }
    .label {
      font-size: 0.88rem;
      color: $color-gray;
    }
  }
  .button {
    height: 3.06rem;
    line-height: 3.06rem;
    width: 100%;
    text-align: center;
    border-radius: 0.38rem;
    background: $color-primary;
    color: #fff;
    font-size: 0.81rem;
    margin-top: 2rem;
  }
  .input_wrap {
    border: 0.03rem solid $uni-color-primary;
    padding: 0.12rem 0.5rem;
    border-radius: 0.16rem;
  }
}

::v-deep .van-stepper__minus,
::v-deep .van-stepper__plus {
  width: 1.63rem;
  height: 1.63rem;
  border: 0.06rem solid #7d8729;
  border-radius: 50%;
  background: transparent;
  &::before {
    background: #7d8729;
  }
  &::after {
    background: #7d8729;
  }
}
::v-deep .van-stepper__minus--disabled,
::v-deep .van-stepper__plus--disabled {
  border-color: #ccc;
  &::before {
    background: #ccc;
  }
  &::after {
    background: #ccc;
  }
}

::v-deep .van-stepper__input {
  background: transparent;
  margin: 0 0.63rem;
  width: 2.6rem;
}
</style>
