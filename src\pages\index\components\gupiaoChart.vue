<template>
  <view id="indexChart" style="width: 100%; height: 154.206px" :style="{ height: height }"></view>
</template>

<script lang="ts" setup>
import { init, dispose } from 'klinecharts'
import { onMounted, onUnmounted, watch } from 'vue'
const props = defineProps({
  value: {
    type: Array,
    default: () => []
  },
  height: {
    type: String,
    default: '154px'
  }
})
let chart: any = null
watch(
  () => props.value,
  (newData) => {
    chart?.applyNewData(newData)
  },
  { deep: true }
)
const chartOption = {
  // 蜡烛图
  candle: {
    type: 'candle_solid',
    // 蜡烛柱
    bar: {
      upColor: '#ea3522',
      downColor: '#78a386',
      noChangeColor: '#888888',
      upBorderColor: '#ea3522',
      downBorderColor: '#78a386',
      noChangeBorderColor: '#888888',
      upWickColor: '#ea3522',
      downWickColor: '#78a386',
      noChangeWickColor: '#888888'
    },
    // 提示
    tooltip: {
      showRule: 'none'
    }
  },
  grid: {
    show: true,
    horizontal: {
      show: true,
      size: 1,
      color: '#333',
      style: 'dashed',
      dashedValue: [2, 2]
    },
    vertical: {
      show: true,
      size: 1,
      color: '#333',
      style: 'dashed',
      dashedValue: [2, 2]
    }
  },
  // x轴
  xAxis: {
    show: true,
    // x轴分割文字
    tickText: {
      color: '#000'
    }
  },
  // y轴
  yAxis: {
    // x轴分割文字
    tickText: {
      color: '#000'
    }
  }
}
onMounted(() => {
  chart = init('indexChart')
  chart.setStyles(chartOption)
})

onUnmounted(() => {
  dispose('chart')
})
// const chartOption = {
//   // 网格线
//   grid: {
//     show: true,
//     horizontal: {
//       show: true,
//       size: 1,
//       color: '#333',
//       style: 'dashed',
//       dashedValue: [2, 2]
//     },
//     vertical: {
//       show: true,
//       size: 1,
//       color: '#333',
//       style: 'dashed',
//       dashedValue: [2, 2]
//     }
//   },
//   // x轴
//   xAxis: {
//     show: true,
//     size: 'auto',
//     // x轴线
//     axisLine: {
//       show: true,
//       color: '#666',
//       size: 1
//     },
//     // x轴分割文字
//     tickText: {
//       show: true,
//       color: '#D9D9D9',
//       family: 'Helvetica Neue',
//       weight: 'normal',
//       size: 12,
//       marginStart: 4,
//       marginEnd: 4
//     },
//     // x轴分割线
//     tickLine: {
//       show: true,
//       size: 1,
//       length: 3,
//       color: '#666'
//     }
//   },
//   // y轴
//   yAxis: {
//     show: true,
//     size: 'auto',
//     // 'left' | 'right'
//     position: 'right',
//     // 'normal' | 'percentage' | 'log'
//     type: 'normal',
//     inside: false,
//     reverse: false,
//     // y轴线
//     axisLine: {
//       show: true,
//       color: '#666',
//       size: 1
//     },
//     // x轴分割文字
//     tickText: {
//       show: true,
//       color: '#D9D9D9',
//       family: 'Helvetica Neue',
//       weight: 'normal',
//       size: 12,
//       marginStart: 4,
//       marginEnd: 4
//     },
//     // x轴分割线
//     tickLine: {
//       show: true,
//       size: 1,
//       length: 3,
//       color: '#666'
//     }
//   },
//   // 图表之间的分割线
//   separator: {
//     size: 1,
//     color: '#666',
//     fill: true,
//     activeBackgroundColor: 'rgba(230, 230, 230, .15)'
//   },
//   crosshair: {
//     show: true,
//     // 十字光标水平线及文字
//     horizontal: {
//       show: true,
//       line: {
//         show: true,
//         style: 'solid',
//         size: 1,
//         color: '#333'
//       }
//     },
//     vertical: {
//       show: true,
//       line: {
//         show: true,
//         style: 'solid',
//         size: 1,
//         color: '#333'
//       }
//     }
//   }
// }
</script>
<style scoped lang="scss"></style>
