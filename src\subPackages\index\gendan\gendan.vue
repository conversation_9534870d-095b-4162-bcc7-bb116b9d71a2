<template>
  <Navigater :is-show-back="false" :title="t('gendan.title')" style="background: transparent; color: black">
    <template #left>
      <img class="back" src="/static/arrow-left.png" @click="goBack" />
      <!-- <view class="record" @click="goPage('./record')">{{ t('gendan.jilu') }}</view> -->
    </template>
    <template #right>
      <img class="record" src="/static/3.png" @click="goPage('./record')" />
      <!-- <view class="record" @click="goPage('./record')">{{ t('gendan.jilu') }}</view> -->
    </template>
  </Navigater>
  <NotData v-if="gupiaoList.length === 0"></NotData>
  <scroll-view v-else scroll-y :style="{ height: `calc(${store.pageHeight} - 60px)` }">
    <view class="box_list">
      <view v-for="(item, index) in gupiaoList" :key="index" class="box">
        <view class="title-box">
          <view class="title-box-left">{{ item.issuer }}</view>
          <view class="title-box-right">{{ changeMoney(item.fund_size) }}</view>
        </view>
        <view class="info-box">
          <view class="info-box-left">
            <view class="name">{{ t('gendan.name') }}: {{ item.product_name }}</view>
            <view class="money"
              >{{ t('gendan.yingli') }} <span>{{ changeMoney(item.estimated_profit) }}%</span></view
            >
          </view>
          <view class="info-box-right" @click="openWindow(item)">
            {{ t('gendan.button') }}
          </view>
        </view>
        <view class="time-box">
          <view class="time"> {{ t('gendan.jiezhishijian') }}{{ item.end_time }} </view>
        </view>
      </view>
      <!-- <view class="row">
          <view class="info">
            <view>
              <view class="label">{{ t('gendan.fadanren') }}：</view>
              <view>{{ item.issuer }}</view>
            </view>
            <view>
              <view class="label">{{ t('gendan.name') }}：</view>
              <view>{{ item.product_name }}</view>
            </view>
            <view>
              <view class="label">{{ t('gendan.zijinguimo') }}：</view>
              <view>{{ changeMoney(item.fund_size) }}</view>
            </view>
            <view>
              <view class="label">{{ t('gendan.jiezhishijian') }}：</view>
              <view>{{ item.end_time }}</view>
            </view>
          </view>
          <view class="button" @click="openWindow(item)">{{ t('gendan.button') }}</view>
        </view>
        <view class="yingli">
          <view class="label">{{ t('gendan.yingli') }}：</view>
          <view class="text-red">{{ changeMoney(item.estimated_profit) }}%</view>
        </view> -->
    </view>
  </scroll-view>

  <van-popup v-model:show="gupiaoWindowShow" class="buy_window" v-bind="windowOptions">
    <view class="row_wrap">
      <view class="row">
        <view class="label">{{ t('gendan.fadanren') }}：</view>
        <view class="value">{{ windowDetail.product_name }}</view>
      </view>
      <view class="row">
        <view class="label">{{ t('gendan.zijinguimo') }}：</view>
        <view class="value">{{ changeMoney(windowDetail.fund_size) }}</view>
      </view>
      <view class="row">
        <view class="label">{{ t('gendan.zuiditouzi') }}：</view>
        <view class="value">{{ changeMoney(windowDetail.mini_buy_in) }}</view>
      </view>
      <view class="row">
        <view class="label">{{ t('gendan.mairujine') }}：</view>
        <view class="value">
          <van-field v-model="buyParams.money" type="number" :placeholder="t('gendan.placeholder')" />
        </view>
      </view>
    </view>

    <view class="button" @click="buy">{{ t('gupiaoDetail.submit') }}</view>
  </van-popup>
</template>

<script setup lang="ts">
import { getGendanListApi, buyGendanApi } from '@/api/index/index'
import { onLoad } from '@dcloudio/uni-app'
import { useI18n } from 'vue-i18n'
import { changeMoney, goPage } from '@/common/common'
import { ref } from 'vue'
import { showToast } from 'vant'
import { useCounterStore } from '@/store/store'
const store = useCounterStore()
const { t } = useI18n()

onLoad(() => {
  getGendanList()
})

const goBack = () => {
  uni.navigateBack({})
}

// 獲取新股列表
const gupiaoList = ref([])
const getGendanList = async () => {
  const res = await getGendanListApi()
  gupiaoList.value = res.data.data
}

// 彈窗
const gupiaoWindowShow = ref(false)
const windowOptions = {
  position: 'bottom',
  'close-on-click-overlay': true,
  'safe-area-inset-bottom': true,
  round: true
}
const windowDetail = ref({
  hedging_price: 0
})
const openWindow = (e: any) => {
  windowDetail.value = e
  gupiaoWindowShow.value = true
}

// 購買
const buyParams = ref({
  follow_id: 0,
  money: null
})
const buy = async () => {
  if (!buyParams.value.money) {
    gupiaoWindowShow.value = false
    showToast(t('gendan.warning1'))
    return
  }
  const reg = /((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/
  if (!reg.test(buyParams.value.money)) {
    gupiaoWindowShow.value = false
    showToast(t('gendan.warning2'))
    return
  }
  buyParams.value.follow_id = windowDetail.value.id
  const res = await buyGendanApi(buyParams.value)
  gupiaoWindowShow.value = false
  buyParams.value.money = null
  getGendanList()
  if (res.code === 1) {
    showToast(res.msg)
  }
}
</script>

<style scoped lang="scss">
uni-page-body {
  height: 100%;
  background: linear-gradient(191deg, #c6dbff 0%, #f5f7fb 100%);
}

.box_list {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 0.3125rem;
}

.box {
  width: 21.25rem;
  height: 10.1875rem;
  padding: 1.3125rem 1.25rem 0.9375rem;
  background: #ffffff;
  box-shadow: 0rem 0.25rem 0.625rem 0rem rgba(43, 80, 237, 0.06);
  border-radius: 0.8125rem;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  align-items: center;
  margin-bottom: 0.9375rem;

  .time-box {
    width: 100%;
    margin-top: 0.625rem;
  }
  .time {
    text-align: left;
    font-weight: 400;
    font-size: 0.8125rem;
    color: #a0aabb;
  }
  .info-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    // width: 18.75rem;
    height: 4.0938rem;
    background: #f5f5f5;
    border-radius: 0.375rem;
    border: 0.0313rem solid #f2f2f2;
    margin-top: 0.6563rem;
    padding: 0.625rem 0.9375rem 0.9375rem;

    .info-box-right {
      width: 3.75rem;
      height: 1.6875rem;
      line-height: 1.6875rem;
      text-align: center;
      background: #0c68ff;
      border-radius: 0.8438rem;
      font-weight: 500;
      font-size: 0.8125rem;
      color: #ffffff;
      letter-spacing: 0.0625rem;
    }

    .info-box-left {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      // align-items: center;
      .name {
        font-weight: 500;
        font-size: 30rpx;
        color: #1e1f22;
        line-height: 42rpx;
        text-align: left;
        font-style: normal;
      }
      .money {
        font-weight: 400;
        font-size: 26rpx;
        color: #9ea0a0;
        line-height: 37rpx;
        text-align: left;
        font-style: normal;
        span {
          color: #db503f;
        }
      }
    }
  }

  .title-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title-box-left {
      font-weight: 600;
      font-size: 1rem;
      color: #2a2a2a;
      line-height: 1.4063rem;
      text-align: left;
      font-style: normal;
    }
    .title-box-right {
      font-weight: 600;
      font-size: 1.125rem;
      color: #db503f;
      line-height: 1.5625rem;
      text-align: right;
      font-style: normal;
    }
  }
}
.back {
  position: absolute;
  top: 1rem;
  left: 1.0625rem;
  width: 0.625rem;
  height: 1.0625rem;
}

.record {
  position: absolute;
  // height: 100%;
  width: 0.875rem;
  height: 1rem;
  // padding: 0 1rem;
  top: 1.0313rem;
  right: 0.9375rem;
}
.gupiao_list {
  .gupiao {
    margin: 0.52rem;
    padding: 0.52rem 0.9rem;
    border-radius: 0.62rem;
    background-color: #fff;
    text-align: center;
    .row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .info {
        > view {
          text-align: left;
          display: flex;
        }
        .label {
          color: $uni-text-color-gray;
        }
      }
      .button {
        background-color: $uni-color-primary;
        color: #fff;
        height: 1.4rem;
        line-height: 1.4rem;
        padding: 0 0.7rem;
        border-radius: 0.3rem;
      }
    }
    .yingli {
      display: flex;
      justify-content: center;
      border-top: 0.06rem solid $uni-text-color-gray;
      padding-top: 0.3rem;
      margin-top: 0.1rem;
    }
  }
}

.buy_window {
  background-color: #fff;
  padding: 0 0.81rem 1.25rem 0.81rem;
  .title {
    text-align: center;
    padding: 0.8rem 0 0.5rem;
    font-size: 1rem;
  }
  .row_wrap {
    padding-top: 1rem;
    .row {
      display: flex;
      justify-content: space-between;
      padding: 0.24rem 0.56rem;
      color: #fff;
      view {
        color: $uni-text-color;
        font-size: 0.775rem;
      }
      ::v-deep .van-cell {
        padding: 0 !important;
      }
      ::v-deep .van-field__control {
        text-align: right;
      }
    }
  }
  .button {
    height: 2.19rem;
    line-height: 2.19rem;
    width: 100%;
    text-align: center;
    border-radius: 1.03rem;
    background-color: $uni-color-primary;
    color: #fff;
    font-size: 0.81rem;
    margin-top: 0.75rem;
  }
}
</style>
