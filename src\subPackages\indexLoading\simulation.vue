<template>
  <div class="container">
    <img class="bg" :src="bg" />
    <!-- <div class="title">{{ t('indexLoading.title') }}</div> -->
    <view class="button" @click="goPage('/subPackages/login/login')">{{ t('login.button_text') }}</view>
    <view class="button circle_button" @click="goPage('/subPackages/register/register')">{{ t('register.tip4') }}</view>
    <view class="button circle_button" @click="goPage('/subPackages/register/simulation')">{{ t('indexLoading.moni') }}</view>
  </div>
</template>

<script lang="ts" setup>
import { goPage } from '@/common/common'
import bg from '@/static/logo2.png'
import { onLoad } from '@dcloudio/uni-app'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()

onLoad(() => {
  uni.removeStorageSync('token')
  uni.removeStorageSync('userId')
  uni.removeStorageSync('userInfo')
  uni.removeStorageSync('tabbarHeight')
})
</script>

<style lang="scss" scoped>
.container {
  height: calc(var(--vh) * 100);
  //   background: url('@/static/image/login/bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  overflow: hidden;
  .bg {
    width: 8.13rem;
    margin: 7.5rem auto;
    object-fit: contain;
  }
  .title {
    font-size: 2.2rem;
    font-weight: bold;
    text-align: center;
    color: $color-primary;
    margin-bottom: 7.5rem;
  }
  .button {
    height: 3.75rem;
    background: $color-primary;
    border-radius: 2rem;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    margin: 0 1.69rem;
    font-size: 1.13rem;
  }
  .circle_button {
    border: 0.06rem solid #e3e8ef;
    background: #fff !important;
    color: $color-primary;
    margin-top: 1.25rem;
  }
}
</style>
