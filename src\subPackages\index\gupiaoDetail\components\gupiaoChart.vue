<template>
  <view id="chart" style="width: 100%; height: 30.31rem"></view>
</template>

<script lang="ts" setup>
import { init, dispose } from 'klinecharts'
import { onMounted, onUnmounted, watch } from 'vue'
const props = defineProps({
  value: {
    type: Array,
    default: () => []
  }
})
let chart: any = null
watch(
  () => props.value,
  (newData) => {
    chart?.applyNewData(newData)
  },
  { deep: true }
)
const chartOption = {
  // 蜡烛图
  // candle: {
  //   type: 'candle_solid',
  //   // 蜡烛柱
  //   bar: {
  //     upColor: '#ea3522',
  //     downColor: '#78a386',
  //     noChangeColor: '#888888',
  //     upBorderColor: '#ea3522',
  //     downBorderColor: '#78a386',
  //     noChangeBorderColor: '#888888',
  //     upWickColor: '#ea3522',
  //     downWickColor: '#78a386',
  //     noChangeWickColor: '#888888'
  //   },
  //   // 提示
  //   tooltip: {
  //     showRule: 'none'
  //   }
  // },
  // // x轴
  // xAxis: {
  //   show: true,
  //   // x轴分割文字
  //   tickText: {
  //     color: '#fff'
  //   }
  // },
  // // y轴
  // yAxis: {
  //   // x轴分割文字
  //   tickText: {
  //     color: '#fff'
  //   }
  // }

  grid: {
    show: true,
    horizontal: {
      show: false,
      size: 1,
      color: '#333',
      style: 'dashed',
      dashedValue: [2, 2]
    },
    vertical: {
      show: false,
      size: 1,
      color: '#333',
      style: 'dashed',
      dashedValue: [2, 2]
    }
  },
  // 蜡烛图
  candle: {
    type: 'candle_solid',
    // 网格线
    // 提示
    tooltip: {
      showRule: 'none'
    },
    bar: {
      upColor: '#F92855',
      downColor: '#2DC08E',
      upBorderColor: '#F92855',
      downBorderColor: '#2DC08E',
      upWickColor: '#F92855',
      downWickColor: '#2DC08E'
    }
  },
  indicator: {
    bars: [
      {
        upColor: '#F92855',
        downColor: '#2DC08E'
      }
    ]
  },
  // x轴
  xAxis: {
    // x轴线
    axisLine: {
      show: true,
      color: '#e5e8f6'
    }
  },
  // y轴
  yAxis: {
    // y轴线
    axisLine: {
      color: '#e5e8f6'
    },
    // x轴分割文字
    tickText: {
      color: '#666'
    },
    // x轴分割线
    tickLine: {
      color: '#e5e8f6'
    }
  },
  // 图表之间的分割线
  separator: {
    color: '#e5e8f6'
  }
}
onMounted(() => {
  chart = init('chart')
  chart.setStyles(chartOption)
  chart.createIndicator('MA', false, { id: 'candle_pane' })
  chart.createIndicator('VOL')
  chart.createIndicator('KDJ')
})

onUnmounted(() => {
  dispose('chart')
})
</script>
<style scoped lang="scss"></style>
