<template>
  <div class="bg">
    <div class="circle">
      <div class="inside" :class="result">
        <div class="card">
          <div v-for="(item, index) in list" :key="index" class="block">
            <div class="image_wrap">
              <image :src="item.img" mode="aspectFit" />
            </div>
            <div class="label">{{ item.label }}</div>
            <div class="amount">{{ item.amount }}</div>
          </div>
        </div>
      </div>
      <div class="lamp" :class="{ lamp2: loading }"></div>
      <div class="btn" @click="star"></div>
    </div>

    <div class="bgTitle"></div>
    <div class="bgTitle2"></div>
    <div class="bottom"></div>
    <div class="bottom2">
      <div class="info btn">
        <image src="/static/image/index/zhuanpan/info.png" mode="" />
        <span>ルールの説明</span>
      </div>
      <div class="record btn">
        <image src="/static/image/index/zhuanpan/record.png" mode="" />
        <span>優勝記録</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
// import { getLotteryApi, getLotteryCountApi, getLotteryDrawApi, getLotteryHistoryApi } from '@/api/index'
import jp1 from '@/static/image/index/zhuanpan/jp1.png'
import jp2 from '@/static/image/index/zhuanpan/jp2.png'
import jp3 from '@/static/image/index/zhuanpan/jp3.png'
// import { onShow } from '@dcloudio/uni-app'

// onShow(() => {
//   getLotteryList()
// })
// const lotteryList = ref([])
// const lotteryCount = ref(0)
// const lotteryHistoryList = ref([])

// // 获取抽奖列表
// const getLotteryList = async() => {
//   const res = await getLotteryApi()
//   console.log(res)
// }

// // 获取抽奖次数
// const getLotteryCount = async() => {
//   const res = await getLotteryCountApi()
//   console.log(res)
// }

// // 获取抽奖历史
// const getLotteryHistoryList = async() => {
//   const res = await getLotteryHistoryApi()
//   console.log(res)
// }

const list = ref([
  { label: '11K金のインゴット', img: jp1, amount: '50g' },
  { label: '22K金のインゴット', img: jp2, amount: '50g' },
  { label: '33K金のインゴット', img: jp3, amount: '50g' },
  { label: '44K金のインゴット', img: jp1, amount: '50g' },
  { label: '55K金のインゴット', img: jp2, amount: '50g' },
  { label: '66K金のインゴット', img: jp3, amount: '50g' },
  { label: '77K金のインゴット', img: jp1, amount: '50g' },
  { label: '88K金のインゴット', img: jp2, amount: '50g' },
  { label: '99K金のインゴット', img: jp3, amount: '50g' },
  { label: '00K金のインゴット', img: jp1, amount: '50g' }
])

const result = ref('')
const loading = ref(false)
const star = async () => {
  if (loading.value) {
    return
  }
  loading.value = true
  const random = Math.ceil(Math.random() * 10)
  result.value = `choose${random} animate`
  await delay(5000)

  result.value = `enter${random}`
  await delay(500)
  loading.value = false
}

const delay = (time) => {
  return new Promise((resolve) => setTimeout(resolve, time))
}
</script>

<style lang="scss" scoped>
.bg {
  background: url('@/static/image/index/zhuanpan/bg2.png');
  height: calc(var(--vh) * 100);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  overflow: hidden;
  position: relative;
  .bgTitle {
    background: url('@/static/image/index/zhuanpan/title.png');
    width: 100%;
    height: 10.5rem;
    background-size: 50% auto;
    background-repeat: no-repeat;
    overflow: hidden;
    top: 1.25rem;
    left: 25%;
    position: absolute;
  }
  .bgTitle2 {
    background: url('@/static/image/index/zhuanpan/title2.png');
    width: 100%;
    height: 10.5rem;
    background-size: 100% auto;
    background-repeat: no-repeat;
    overflow: hidden;
    top: 6.25rem;
    position: absolute;
    z-index: 10;
  }
  .bottom {
    background: url('@/static/image/index/zhuanpan/bottom.png');
    width: 100%;
    height: 40.5rem;
    background-size: 100% auto;
    background-repeat: no-repeat;
    overflow: hidden;
    bottom: 0;
    position: absolute;
    z-index: 0;
  }
  .bottom2 {
    background: url('@/static/image/index/zhuanpan/bottom2.png');
    width: 100%;
    height: 16.5rem;
    background-size: 100% auto;
    background-repeat: no-repeat;
    overflow: hidden;
    bottom: 0;
    position: absolute;
    z-index: 100;
    display: flex;
    padding: 13rem 0.94rem 0;
    justify-content: space-between;
    > div {
      display: flex;
      align-items: center;
      gap: 0.38rem;
      span {
        color: $color-green;
      }
    }
    image {
      width: 1.75rem;
      height: 1.75rem;
    }
  }
}

.circle {
  background: url('@/static/image/index/zhuanpan/circle.png');
  width: 20.63rem;
  height: 20.63rem;
  background-size: 100% 100%;
  margin: 0 auto;
  // margin-top: 10.25rem;
  position: absolute;
  left: calc((100vw - 20.63rem) / 2);
  top: calc((var(--vh) * 100 - 20.63rem) / 2);
  z-index: 1000;
  .inside {
    width: 18rem;
    height: 18rem;
    background: url('@/static/image/index/zhuanpan/inside.png');
    background-size: 100% 100%;
    left: 1.315rem;
    top: 1.315rem;
    position: absolute;
    z-index: 100;
    transform: rotate(0);
  }
  .animate {
    transition: all 5s;
  }
  .choose1 {
    transform: rotate(1836deg);
  }
  .choose2 {
    transform: rotate(1872deg);
  }
  .choose3 {
    transform: rotate(1908deg);
  }
  .choose4 {
    transform: rotate(1944deg);
  }
  .choose5 {
    transform: rotate(1980deg);
  }
  .choose6 {
    transform: rotate(2016deg);
  }
  .choose7 {
    transform: rotate(2052deg);
  }
  .choose8 {
    transform: rotate(2088deg);
  }
  .choose9 {
    transform: rotate(2124deg);
  }
  .choose10 {
    transform: rotate(1800deg);
  }
  .enter1 {
    transform: rotate(36deg);
  }
  .enter2 {
    transform: rotate(72deg);
  }
  .enter3 {
    transform: rotate(108deg);
  }
  .enter4 {
    transform: rotate(144deg);
  }
  .enter5 {
    transform: rotate(180deg);
  }
  .enter6 {
    transform: rotate(216deg);
  }
  .enter7 {
    transform: rotate(252deg);
  }
  .enter8 {
    transform: rotate(288deg);
  }
  .enter9 {
    transform: rotate(324deg);
  }
  .enter10 {
    transform: rotate(0);
  }
  .card {
    width: 18rem;
    height: 18rem;
    position: relative;
    .block {
      position: absolute;
      height: 18rem;
      width: 2.2rem;
      left: 7.9rem;
      &:nth-last-of-type(1) {
        transform: rotate(36deg);
      }
      &:nth-last-of-type(2) {
        transform: rotate(72deg);
      }
      &:nth-last-of-type(3) {
        transform: rotate(108deg);
      }
      &:nth-last-of-type(4) {
        transform: rotate(144deg);
      }
      &:nth-last-of-type(5) {
        transform: rotate(180deg);
      }
      &:nth-last-of-type(6) {
        transform: rotate(216deg);
      }
      &:nth-last-of-type(7) {
        transform: rotate(252deg);
      }
      &:nth-last-of-type(8) {
        transform: rotate(288deg);
      }
      &:nth-last-of-type(9) {
        transform: rotate(324deg);
      }
      .image_wrap {
        width: 2.13rem;
        height: 2.13rem;
        margin-top: 0.63rem;
      }
      image {
        width: 2.13rem;
        height: 2.13rem;
      }
      .label {
        font-size: 0.56rem;
        color: #00945c;
        font-weight: 500;
        text-align: center;
      }
      .amount {
        font-size: 0.56rem;
        color: #00945c;
        font-weight: 500;
        text-align: center;
      }
    }
  }
  .lamp {
    width: 20.63rem;
    height: 20.63rem;
    position: absolute;
    left: 0;
    top: 0;
    background: url('@/static/image/index/zhuanpan/lamp.png');
    background-size: 100% 100%;
  }
  .lamp2 {
    // background: url('@/static/image/index/zhuanpan/lamp.gif');
    background-size: 100% 100%;
  }
  .btn {
    width: 5.31rem;
    height: 6.56rem;
    position: absolute;
    left: 0;
    top: 0;
    background: url('@/static/image/index/zhuanpan/btn.png');
    background-size: 100% 100%;
    z-index: 1000;
    left: 7.66rem;
    top: 6.6rem;
  }
}
</style>
