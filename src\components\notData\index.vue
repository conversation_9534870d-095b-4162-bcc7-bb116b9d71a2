<script setup lang="ts">
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const props = defineProps({
  title: {
    type: Number,
    default: 0
  }
})
</script>
<template>
  <view class="nodataWrap">
    <view class="box">
      <image src="/static/nodata.png" class="img" />
      <view v-if="!props.title" class="title">{{ t('common.nodata') }}</view>
      <view v-else class="title">{{ props.title }}</view>
    </view>
  </view>
</template>
<style lang="scss" scoped>
.nodataWrap {
  overflow: hidden;
}
.box {
  margin: 5rem auto 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .img {
    width: 15.63rem;
    height: 15.63rem;
  }
  .title {
    color: #a6a6a6;
    font-size: 0.75rem;
    margin-top: 1.25rem;
    position: relative;
    bottom: 1.88rem;
  }
}
</style>
