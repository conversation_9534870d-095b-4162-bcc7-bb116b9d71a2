<template>
  <view class="box">
    <view class="top">
      <view class="user-info-left">
        <img class="headImg" src="/static/image/user/touxiang.png" alt="" />
        <view class="user-info-box">
          <view style="color: #222; font-size: 1rem; font-weight: 500">{{ desensitization(store.$state.userInfo?.mobile) }}</view>
          <view style="font-size: 0.8125rem; color: #a6a6a6">{{ store.$state.userInfo?.true_name || t('index.tip1') }}</view>
          <span style="font-size: 0.8125rem; color: #a6a6a6">{{ t('user.xinyongpingfen') }}：{{ store.$state.userInfo?.score }}</span>
        </view>
      </view>
      <view class="user-info-right" @click="goPage('/subPackages/seting/seting')">
        <img :src="set" style="width: 1.0211rem; height: 0.875rem" />
      </view>
    </view>

    <view class="user-info">
      <view class="user-info-top">
        <view class="user-info-top-left">
          <view class="zichan-box">
            <view style="display: flex; align-items: center">
              <span class="zichan-box-title">{{ t('user.zongzichan') }}</span>
              <van-icon v-if="!isShowInfo" name="closed-eye" color="#fff" size="20" class="showIcon" @click="isShowInfo = !isShowInfo" />
              <van-icon v-else name="eye-o" color="#fff" size="20" class="showIcon" @click="isShowInfo = !isShowInfo" />
            </view>
            <span v-if="!isShowInfo" class="zichan-box-money">******</span>
            <span v-else class="zichan-box-money">{{ changeMoney(Number(store.$state.userInfo?.total)) || 0 }}</span>
          </view>
        </view>
      </view>
      <view class="user-info-buttom">
        <view v-for="(item, i) in userAssetsInfo" :key="i" class="user-info-item" :style="{ width: i == 0 ? '40%' : '30%' }">
          <view class="user-info-item-left" :style="i === 0 ? { textAlign: 'left' } : i === 1 ? { textAlign: 'center' } : { textAlign: 'right' }">
            <view class="user-info-item-t">{{ t(item.t) }}</view>
          </view>
          <view v-if="!isShowInfo" class="user-info-item-money" :style="i === 0 ? { textAlign: 'left' } : i === 1 ? { textAlign: 'center' } : { textAlign: 'right' }">******</view>
          <view v-else class="user-info-item-money" :style="{ color: item.color, wordWrap: breakWord }"
            ><view :style="i === 0 ? { textAlign: 'left' } : i === 1 ? { textAlign: 'center' } : { textAlign: 'right' }"> {{ changeMoney(item.num) }}</view></view
          >
        </view>
      </view>
      <view class="user-info-top-right">
        <div class="button" @click="goWeb">
          <image :src="recharge" mode="" />
          <div class="label">{{ t('user.chongzhi') }}</div>
        </div>
        <div class="button" @click="goPage('/subPackages/withdrawal/withdrawal')">
          <image :src="withdraw" mode="" />
          <div class="label">{{ t('user.tixian') }}</div>
        </div>
        <!-- <van-button class="chongzhi" type="primary" @click="goWeb">{{ t('user.chongzhi') }}</van-button>
        <van-button type="primary" plain class="plain-btn" @click="goPage('/subPackages/withdrawal/withdrawal')">{{ t('user.tixian') }}</van-button> -->
      </view>
    </view>

    <view class="page-list-box">
      <view v-for="(item, i) in pageListArr" :key="i" class="page-list-item" @click="item.name === 'user.zhuxiao' ? store.clearState() : goPage(item.url, i == 5 ? true : false)">
        <view style="height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: flex-start">
          <div class="image_wrap">
            <image class="page-list-item-icon" :src="item.imgUrl" />
          </div>
          <view class="name" :style="{ color: item.color }">{{ t(item.name) }}</view>
        </view>
        <!-- <img :src="right" style="width: 0.4063rem; height: 0.75rem" /> -->
        <!-- <van-icon name="arrow"></van-icon> -->
      </view>
    </view>
  </view>
  <CustomTabbar :id="4" />
</template>

<script lang="ts" setup>
import CustomTabbar from '@/components/custom-tabbar/custom-tabbar.vue'
import { useI18n } from 'vue-i18n'
import { computed, ref } from 'vue'
import { useCounterStore } from '@/store/store'
import { onShow } from '@dcloudio/uni-app'
import { getCzhiurlApi } from '@/api/user'
import { changeMoney } from '@/common/common'
import set from '@/static/image/user/set.png'
import recharge from '@/static/image/user/recharge.png'
import withdraw from '@/static/image/user/withdraw.png'
const store = useCounterStore()
// 基于准备好的dom，初始化echarts实例

const kefu_url = ref('')

const getCzhiurlFn = async () => {
  const res = await getCzhiurlApi()
  if (res.code === 1) {
    kefu_url.value = res.data.kefu_url
  }
}

// 手机号脱敏
const desensitization = (str: string) => {
  return str
    .split('')
    .map((item, i) => {
      if (i >= 3 && i <= 6) {
        return '*'
      }
      return item
    })
    .join('')
}

const goWeb = () => {
  window.open(kefu_url.value)
}
onShow(() => {
  getCzhiurlFn()
  store.getUserInfo()
})

const goPage = (url: string, is = false) => {
  if (is) {
    goWeb()
    return
  }
  uni.navigateTo({ url })
}

const { t } = useI18n()
const isShowInfo = ref(false)
const pageListArr = [
  {
    name: 'user.yinhangzhanghu',
    imgUrl: '/static/image/user/1.png',
    url: '/subPackages/bankAccount/bankAccount'
    // url: '/subPackages/index/zhuanpan/zhuanpan'
  },
  {
    name: 'user.shouru',
    imgUrl: '/static/image/user/2.png',
    url: '/subPackages/transactionLog/transactionLog'
  },
  {
    name: 'user.benrenqueren',
    imgUrl: '/static/image/user/3.png',
    url: '/subPackages/real_name/real_name'
  },
  {
    name: 'user.rizhisuoyin',
    imgUrl: '/static/image/user/4.png',
    url: '/subPackages/changePassword/changePassword'
  },
  {
    name: 'user.shiwuchuli',
    imgUrl: '/static/image/user/5.png',
    url: '/subPackages/transactionPassions/transactionPassions'
  },
  {
    name: 'user.kefuzhongxin',
    imgUrl: '/static/image/user/6.png',
    url: '/subPackages/customerService/customerService'
  },
  // {
  //   name: 'user.qiehuanyuyan',
  //   imgUrl: '/static/image/user/7.png',
  //   url: '/subPackages/switchLanguage/switchLanguage'
  // },
  {
    name: 'user.zhuxiao',
    imgUrl: '/static/image/user/8.png',
    url: '/subPackages/logout/logout',
    color: '#FF6254'
  }
]
const money = computed(() => {
  return store.$state.userInfo?.money || 0
})
const count_market = computed(() => {
  return store.$state.userInfo?.shizhi || 0
})
const count_losses = computed(() => {
  return store.$state.userInfo?.my_losses?.count_losses || 0
})

const userAssetsInfo = ref([
  {
    t: 'user.xianjinyue',
    num: money,
    tou: '#50AFF0',
    color: '#fff'
  },
  {
    t: 'user.beiyongzijin',
    num: count_market,
    tou: '#50F0AA',
    color: '#fff'
  },
  {
    t: 'user.fudongsunyi',
    num: count_losses,
    tou: '#C850F0',
    color: '#fff'
  }
])
</script>
<style scoped lang="scss">
::v-deep .van-button--normal {
  padding: 0;
}
uni-view {
  box-sizing: border-box;
}
* {
  font-weight: 500;
}
::v-deep .van-button {
  width: 4.0625rem;
  height: 1.5rem;
  border-radius: 1.5625rem;

  .van-button__text {
    font-size: $uni-font-size-1;
  }
}

.box {
  padding-bottom: 1.875rem;
  .page-list-box {
    margin-top: 1.5625rem;
    display: flex;
    flex-wrap: wrap;
    padding: 0 0.9375rem;
    justify-content: flex-start;

    .page-list-item {
      // width: 21.5625rem;
      // margin: 0 0.94rem;
      width: 22%;
      // height: 3.6875rem;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      border-bottom: 0.05rem solid #ffffff50;
      // background: #f8fafc;
      // margin-bottom: 0.31rem;
      border-radius: 0.75rem;
      margin-left: 0.625rem;

      &:nth-child(4n + 1) {
        margin-left: 0;
      }
      .image_wrap {
        // width: 1.44rem;
        // height: 1.44rem;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 0.625rem;
      }
      .page-list-item-icon {
        width: 1.875rem;
        height: 1.875rem;
      }
      .name {
        white-space: pre-wrap;
        color: $color-black;
        font-size: 0.875rem;
        text-align: center;
      }
    }
  }

  .user-info {
    width: 21.5625rem;
    height: 14.6875rem;
    border-radius: 1.875rem;
    background: linear-gradient(180deg, #ff1645 0%, #bb0025 100%);
    // background-image: url(/src/static/image/user/bg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    // height: 16rem;
    margin: 0rem auto 0;
    padding: 0.625rem 0.94rem;

    .user-info-buttom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 1.5625rem;
    }

    .user-info-top {
      display: flex;
      width: 100%;
      // height: 6.375rem;
      border-radius: 1.25rem;
      // border: 2px solid #00f0d0;
      // background: linear-gradient(94deg, #2baa99 0.49%, #019682 99.51%);
      justify-content: flex-start;
      align-items: center;
      .user-info-top-left {
        .zichan-box {
          display: flex;
          flex-direction: column;
          align-items: self-start;
          // align-items: center;
          position: relative;
          .showIcon {
            margin-left: 0.625rem;
          }

          .zichan-box-title {
            font-size: 0.8125rem;
            color: #fff;
          }

          .zichan-box-money {
            font-weight: 700;
            font-size: 2.25rem;
            color: #fff;
            text-align: center;
          }
        }
      }
    }
    .user-info-btns {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 1.2813rem;
      ::v-deep .van-button--primary .van-button__text {
        color: #6a69e9;
      }
      ::v-deep .van-button--primary.plain-btn .van-button__text {
        color: #0e0c0c;
      }
    }

    .user-info-item {
      width: 33%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-bottom: 1.25rem;
      color: #fff;

      .user-info-item-money {
        width: 100%;
        line-height: 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        color: #fff;
        margin-top: 0.625rem;
        word-wrap: break-word;
        text-align: center;
      }

      .user-info-item-left {
        text-align: center;
        .user-info-item-t {
          font-size: 0.75rem;
          line-height: 0.875rem;
          color: #fff;
        }

        .user-info-item-tou {
          width: 0.4063rem;
          height: 0.4063rem;
          border-radius: 50%;
          margin-right: 0.4375rem;
        }
      }
    }
  }

  .user-info-top-right {
    // width: 18.125rem;
    // height: 5.875rem;
    display: flex;
    justify-content: space-around;
    // background-image: url(/src/static/image/user/bg2.png);
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    padding-right: 1.125rem;
    image {
      width: 1.375rem;
      height: 0.875rem;
    }
    .button {
      width: 8.25rem;
      height: 2.8125rem;
      border-radius: 6.25rem;
      background-color: #0f0f0f;
      // border-radius: 1rem;
      text-align: center;
      // padding: 0.81rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 3.125rem;
      // border: 1px solid #ffffff75;
    }
    .label {
      font-size: 0.75rem;
      color: #fff;
      font-weight: 400;
      margin-left: 0.5rem;
    }
  }

  .top {
    width: 100%;
    // height: 3.0625rem;
    padding: 0.9375rem;
    .user-info-bottom {
      position: absolute;
      top: 5.9063rem;
      left: 0.6563rem;
      width: 22.125rem;
      height: 3.625rem;
      border-radius: 1.25rem;
      display: flex;
      justify-content: space-between;

      .zichan-chart {
        width: 2.875rem;
        height: 2.875rem;
        margin-right: 1.0938rem;
        margin-top: 0.3125rem;
      }
    }

    .user-info-right {
      position: absolute;
      top: 2.0313rem;
      right: 0.9375rem;

      font-weight: 500;
      font-size: $uni-font-size-1;
      color: #50aff0;
      display: flex;
      justify-content: center;
      align-items: center;
      .icon-arrow {
        width: 0.375rem;
        height: 0.6563rem;
      }
    }

    .user-info-left {
      display: flex;
      align-items: center;
      .user-info-box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-weight: 500;
        font-size: 1rem;
        color: #161616;
        span {
          height: 0.625rem;
          margin-bottom: 0.3125rem;
          line-height: 0.625rem;
        }
      }
    }

    .headImg {
      border-radius: 50%;
      height: 2.4375rem;
      width: 2.4375rem;
      margin-right: 0.7813rem;
    }
  }
}
</style>
