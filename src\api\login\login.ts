import request from '@/utils/http'
import config from '@/utils/config'
import { loginType, registerType, sendSmsType } from './loginType'

// 登录
export function loginApi(data: loginType) {
  return request({
    url: `${config.baseURL}/user/login`,
    method: 'post',
    data
  })
}

// 注册
export function registerApi(data: registerType) {
  return request({
    url: `${config.baseURL}/user/register`,
    method: 'post',
    data
  })
}

// 验证码
export function sendSmsApi(data: sendSmsType) {
  return request({
    url: `${config.baseURL}/sms/send`,
    method: 'post',
    data
  })
}

// 模拟登录
export function simulationLoginApi(data: loginType) {
  return request({
    url: `${config.baseURL}/user/simulationLogin`,
    method: 'post',
    data
  })
}

// 模拟注册
export function simulationRegisterApi(data: registerType) {
  return request({
    url: `${config.baseURL}/user/simulationRegister`,
    method: 'post',
    data
  })
}
