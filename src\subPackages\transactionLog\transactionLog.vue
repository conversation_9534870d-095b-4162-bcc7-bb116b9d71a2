<script setup lang="ts">
// import SearchNavigation from '@/components/searchNavigation/searchNavigation.vue'
import { onMounted, Ref, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import OrderLog from './components/orderLog.vue'
import IncomeLog from './components/incomeLog.vue'
import { getLiuShuiApi } from '@/api/transactionLog'
import { JyType, CzType } from '@/api/transactionLog/indexType'
import { onLoad, onReachBottom } from '@dcloudio/uni-app'
import Navigator from '@/components/navigator/navigator.vue'
import { closeToast, showLoadingToast } from 'vant'
const { t } = useI18n()

interface TabType {
  title: string
  value: number
}
const isLs = ref(false)
onLoad((option) => {
  console.log(option, 'option')
  if (option?.isLs === '1') {
    isLs.value = true
    tabValue.value = 3
  } else {
    isLs.value = false
    tabValue.value = 1
  }
})

const tabValue: Ref<Number> = ref(1)

const tabList: Ref[TabType] = ref([
  {
    title: 'transactionLog.tab2',
    value: 1
  },
  {
    title: 'transactionLog.tab3',
    value: 2
  }
])

const checkTab = (value: number) => {
  tabValue.value = value
  getLiuShuiFn()
}

onMounted(() => {
  getLiuShuiFn()
})

const jyData: Ref[JyType] = ref([])
let jyPage = 1
const czData: Ref[CzType] = ref([])
let czPage = 1
const tlData: Ref[CzType] = ref([])
let tlPage = 1
const getLiuShuiFn = async () => {
  showLoadingToast({
    message: t('search.tip2'),
    mask: true,
    loadingType: 'spinner'
  })
  isLoading.value = true
  let page = 1
  switch (tabValue.value) {
    case 1:
      page = czPage
      break
    case 2:
      page = tlPage
      break
    case 3:
      page = jyPage
      break
  }
  const res = await getLiuShuiApi({ type: tabValue.value, page })
  closeToast()
  if (res.code === 1) {
    if (tabValue.value === 3) {
      if (res.data.data.length) {
        jyPage++
      }
      res.data.data.forEach((element) => {
        const arr = element.detailed.split('|')
        element.name = arr[0]
        if (arr[1]) {
          element.gpCode = arr[2]
          element.gpName = arr[4]
          element.isshow = true
        } else {
          element.isshow = false
        }

        jyData.value.push(element)
      })
      console.log(jyData.value)
    }

    if (tabValue.value === 1) {
      res.data.data.forEach((element) => {
        czData.value.push(element)
      })
      if (res.data.data.length) {
        czPage++
      }
      console.log(czData.value)
    }
    if (tabValue.value === 2) {
      res.data.data.forEach((element) => {
        tlData.value.push(element)
      })
      if (res.data.data.length) {
        tlPage += 1
      }
    }
  }

  isLoading.value = false
}

const isLoading = ref(false)

onReachBottom(() => {})

const bottomFn = () => {
  getLiuShuiFn()
}
</script>

<template>
  <Navigator class="top" :title="isLs ? t('index.button4') : t('user.shouru')" />
  <view class="bg-[#fff]"> </view>
  <view v-if="!isLs" class="tab-box">
    <view v-for="item in tabList" :key="item.value" class="tab-box-item" :class="tabValue === item.value ? 'active' : ''" @click="checkTab(item.value)">
      {{ t(item.title) }}
    </view>
  </view>

  <OrderLog v-if="isLs && tabValue === 3" :data="jyData" @bottom="bottomFn" />
  <IncomeLog v-if="!isLs && tabValue === 1" :data="czData" :value="tabValue" @bottom="bottomFn" />
  <IncomeLog v-if="!isLs && tabValue === 2" :data="tlData" :value="tabValue" @bottom="bottomFn" />
</template>

<style lang="scss" scoped>
.list {
  background: rgb(10, 30, 54, 0.5);
  border-top-left-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
}

.loading-box {
  display: flex;
  justify-content: center;
  align-content: center;
}
uni-page-body {
  height: 100%;
}
.tab-box {
  width: 100%;
  padding: 0.8125rem 1.3125rem;
  display: flex;
  align-items: center;
  gap: 0.63rem;
  .tab-box-item.active {
    background: $color-primary;
    color: #fff;
  }
  .tab-box-item {
    // width: 6.875rem;
    flex: 1;
    height: 2.13rem;
    padding: 0.25rem 0.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.5rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.875rem;
    color: $color-primary;
    border: 0.05rem solid #e3e8ef;
  }
}
</style>
