<template>
  <view class="container">
    <div class="navigator">
      <view class="back" @click="reLaunch('/subPackages/indexLoading/simulation')">
        <van-icon name="arrow" color="#000" size="1.3rem" />
      </view>
    </div>
    <view class="wrap">
      <view class="title">{{ t('register.title') }}</view>
      <view class="mas">
        <view class="input_wrap" @click="phoneFocus = true">
          <img :src="phone" alt="" />
          <input v-model="registerParams.account" :placeholder="t('login.account_placeholder')" :focus="phoneFocus" @blur="phoneFocus = false" />
        </view>
        <view class="input_wrap" @click="passwordFocus = true">
          <img :src="password" alt="" />
          <input
            v-model="registerParams.password"
            :placeholder="t('register.password_placeholder')"
            :type="showPassword ? 'text' : 'password'"
            :focus="passwordFocus"
            autocomplete="false"
            @blur="passwordFocus = false"
          />
          <img class="pwd" :src="showPassword ? eye : eye_close" alt="" @click.stop="showPassword = !showPassword" />
        </view>
        <view class="input_wrap" @click="rePasswordFocus = true">
          <img :src="password" alt="" />
          <input
            v-model="registerParams.again_password"
            :placeholder="t('register.password_again_placeholder')"
            :focus="rePasswordFocus"
            autocomplete="false"
            :type="showRePassword ? 'text' : 'password'"
            @blur="rePasswordFocus = false"
          />
          <img class="pwd" :src="showRePassword ? eye : eye_close" alt="" @click.stop="showRePassword = !showRePassword" />
        </view>
        <view class="input_wrap" @click="codeFocus = true">
          <img :src="code" alt="" />
          <input v-model="registerParams.yaoqingma" disabled :placeholder="t('register.code_placeholder')" :focus="codeFocus" @blur="codeFocus = false" />
        </view>
      </view>
      <view class="button-wrap">
        <view class="button" @click="register">{{ t('register.tip4') }}</view>
        <!-- <view class="text" @click="reLaunch('/subPackages/login/login')">{{ t('register.tip3') }}</view> -->
        <view class="button circle_button" @click="reLaunch('/subPackages/login/simulation')">{{ t('login.button_text') }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { simulationRegisterApi } from '@/api/login/login'
import { reLaunch, checkInput, switchTab } from '@/common/common'
import { showFailToast, showLoadingToast, showSuccessToast, showToast } from 'vant'
import { useCounterStore } from '@/store/store'
import phone from '@/static/image/login/phone.png'
import password from '@/static/image/login/password.png'
import code from '@/static/image/login/code.png'
import eye from '@/static/image/login/eye.png'
import eye_close from '@/static/image/login/eye-close.png'
const { t } = useI18n()
const store = useCounterStore()

const registerParams = ref({
  account: '',
  password: '',
  again_password: '',
  yaoqingma: '0000'
})

const showPassword = ref(false)
const showRePassword = ref(false)

const phoneFocus = ref(false)
const passwordFocus = ref(false)
const rePasswordFocus = ref(false)
const codeFocus = ref(false)

const checkArr = [
  { key: 'account', message: t('register.account_error') },
  { key: 'password', message: t('register.password_error') },
  { key: 'again_password', message: t('register.password_again_error') },
  { key: 'yaoqingma', message: t('register.code_error') }
]

const register = async () => {
  const reg = /^[A-Za-z0-9]{3,13}$/
  if (!checkInput(checkArr, registerParams.value)) {
    return
  }
  if (!reg.test(registerParams.value.account)) {
    return showToast(t('register.tip11'))
  }

  if (registerParams.value.password.length < 6 || registerParams.value.password.length > 10) {
    return showToast(t('register.tip2'))
  }

  if (registerParams.value.password !== registerParams.value.again_password) {
    showFailToast(t('register.password_repeat_error'))
    return
  }
  showLoadingToast({ mask: true })
  const res = await simulationRegisterApi(registerParams.value)
  // closeToast()
  if (res.code === 1) {
    uni.setStorageSync('token', res.data.userinfo.token)
    uni.setStorageSync('userId', res.data.userinfo.id)
    uni.setStorageSync('userInfo', res.data.userinfo)
    store.token = res.data.userinfo.token
    store.userId = res.data.userinfo.userId
    store.getUserInfo()
    showSuccessToast(res.msg)
    setTimeout(() => {
      switchTab('/pages/index/index')
    }, 1000)
  } else {
    showFailToast(res.msg)
  }
}
</script>

<style lang="scss" scoped>
.navigator {
  width: 100%;
  height: 3.13rem;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 1rem;
  .van-icon {
    transform: rotate(180deg);
  }
}
.button-wrap {
  width: 20rem;
  height: 3.125rem;
  justify-content: space-between;
  align-content: center;
  margin-top: 0.625rem;
  .text {
    color: #a6a6a6;
    font-size: 0.9375rem;
    line-height: 3.125rem;
  }
}
.mas {
  width: 20rem;
  border-radius: 1.25rem;
}
body {
  background: #fff;
}
.wrap {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: calc(var(--vh) * 100 - 3.13rem);
}
.container {
  text-align: center;
  margin: 0 auto;
  //   background: url('@/static/image/login/bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .bg {
    width: 15.7188rem;
    height: 12.8422rem;
    margin: 0.5625rem auto 0;
  }
  .title {
    color: $color-black;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 5.25rem 0 1.75rem;
  }
}
.logo {
  width: 7.5rem;
  height: 7.5rem;
  margin: 14vh auto 0vh;
}
.name {
  margin: 0vh auto 9.54vh;
  font-size: 1.5rem;
  color: $uni-color-primary;
}
.input_wrap {
  display: flex;
  align-items: center;
  height: 3.06rem;
  border-radius: 0.94rem;
  background: #f8fafc;
  margin-bottom: 0.94rem;
  padding: 0 0.88rem;
  max-width: 20rem;
  margin-left: auto;
  margin-right: auto;
  font-size: 1rem;
  gap: 1rem;
  img {
    width: 1.5rem;
    height: 1.5rem;
    object-fit: contain;
  }
  .pwd {
    padding: 0.2rem;
  }
  input {
    flex: 1;
    text-align: left;
    color: #0b121b;
    .uni-input-placeholder {
      color: #afafaf;
    }
  }
}
.button {
  width: 100%;
  height: 3.75rem;
  background-color: $uni-color-primary;
  border-radius: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  background: $color-primary;
  font-weight: 500;
}
.circle_button {
  border: 0.06rem solid #e3e8ef;
  background: #fff !important;
  margin-top: 1.25rem;
  color: $color-primary;
}
</style>
